// --- File: Code.gs ---

const SHEET_ID = "1nmF3iJfQXWZaGo-rAbLEkv8FLirgPeMFpnUHWhgKAoc";
const SHEET_NAME = "StudentData";

/**
 * Serves the HTML file for the web app.
 */
function doGet(e) {
  const htmlTemplate = HtmlService.createTemplateFromFile('index');
  // Pass the script URL to the HTML template
  htmlTemplate.url = ScriptApp.getService().getUrl(); 
  return htmlTemplate.evaluate()
    .setTitle("ระบบแจ้งคะแนนสอบนักเรียน")
    .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}

/**
 * Handles POST requests from the client-side.
 */
function doPost(e) {
  try {
    const sheet = getAndPrepareSheet();
    const requestData = JSON.parse(e.postData.contents);
    const action = requestData.action;

    switch (action) {
      case 'add':
        return addData(sheet, requestData.data);
      case 'edit':
        return editData(sheet, requestData.data);
      case 'delete':
        return deleteData(sheet, requestData.id, requestData.subject);
      case 'getStudent':
        return getStudentData(sheet, requestData.id);
      case 'getAll':
        const allData = getSheetDataAsObjects(sheet);
        return createJsonResponse({ success: true, data: allData });
      case 'importData':
        return importData(sheet, requestData.data);
      default:
        return createJsonResponse({ success: false, message: "Invalid action." });
    }
  } catch (error) {
    Logger.log(error);
    return createJsonResponse({ success: false, message: "Server Error: " + error.toString() });
  }
}

/**
 * Imports data from a CSV file.
 */
function importData(sheet, dataArray) {
  if (!Array.isArray(dataArray) || dataArray.length === 0) {
    return createJsonResponse({ success: false, message: "ไม่มีข้อมูลที่จะนำเข้า" });
  }
  const rowsToAdd = dataArray.map(data => [
    data.id, data.name, data.class, data.subject, 
    data.midterm_score, data.midterm_full, data.midterm_decision, 
    data.final_score, data.final_full, data.final_decision
  ]);
  sheet.getRange(sheet.getLastRow() + 1, 1, rowsToAdd.length, rowsToAdd[0].length).setValues(rowsToAdd);
  return createJsonResponse({ success: true, message: `นำเข้าข้อมูลสำเร็จ ${rowsToAdd.length} รายการ` });
}

/**
 * Adds a new row of data.
 */
function addData(sheet, data) {
  const newRow = [
    data.id, data.name, data.class, data.subject, 
    data.midterm_score, data.midterm_full, data.midterm_decision, 
    data.final_score, data.final_full, data.final_decision
  ];
  sheet.appendRow(newRow);
  return createJsonResponse({ success: true, message: "เพิ่มข้อมูลสำเร็จ" });
}

/**
 * Edits an existing row of data.
 */
function editData(sheet, data) {
  const allData = sheet.getDataRange().getValues();
  const headers = allData[0];
  const idCol = headers.indexOf("รหัสประจำตัวประชาชน");
  const subjectCol = headers.indexOf("รายวิชา");

  for (let i = 1; i < allData.length; i++) {
    if (allData[i][idCol] == data.original_id && allData[i][subjectCol] == data.original_subject) {
      const rowToUpdate = i + 1;
      const updatedRow = [[
        data.id, data.name, data.class, data.subject, 
        data.midterm_score, data.midterm_full, data.midterm_decision, 
        data.final_score, data.final_full, data.final_decision
      ]];
      sheet.getRange(rowToUpdate, 1, 1, headers.length).setValues(updatedRow);
      return createJsonResponse({ success: true, message: "แก้ไขข้อมูลสำเร็จ" });
    }
  }
  return createJsonResponse({ success: false, message: "ไม่พบข้อมูลที่จะแก้ไข" });
}

/**
 * Deletes a specific row.
 */
function deleteData(sheet, id, subject) {
  const data = sheet.getDataRange().getValues();
  const headers = data[0];
  const idCol = headers.indexOf("รหัสประจำตัวประชาชน");
  const subjectCol = headers.indexOf("รายวิชา");

  for (let i = data.length - 1; i >= 1; i--) {
    if (data[i][idCol] == id && data[i][subjectCol] == subject) {
      sheet.deleteRow(i + 1);
      return createJsonResponse({ success: true, message: "ลบข้อมูลสำเร็จ" });
    }
  }
  return createJsonResponse({ success: false, message: "ไม่พบข้อมูลที่จะลบ" });
}

/**
 * Gets all records for a specific student.
 */
function getStudentData(sheet, studentId) {
  const data = getSheetDataAsObjects(sheet);
  const studentRecords = data.filter(row => row.id == studentId);
  if (studentRecords.length === 0) {
    return createJsonResponse({ success: false, message: "ไม่พบข้อมูล กรุณาตรวจสอบรหัสอีกครั้ง" });
  }
  const representativeRecord = studentRecords[0];
  const result = {
    success: true,
    id: representativeRecord.id,
    name: representativeRecord.name,
    class: representativeRecord.class,
    scores: studentRecords
  };
  return createJsonResponse(result);
}

/**
 * Gets or creates the sheet and ensures headers are correct.
 */
function getAndPrepareSheet() {
  const spreadsheet = SpreadsheetApp.openById(SHEET_ID);
  let sheet = spreadsheet.getSheetByName(SHEET_NAME);
  const requiredHeaders = [
    "รหัสประจำตัวประชาชน", "ชื่อ-นามสกุล", "ห้อง/ระดับชั้น", "รายวิชา", 
    "คะแนนกลางภาค", "คะแนนเต็มกลางภาค", "ผลการตัดสินกลางภาค", 
    "คะแนนปลายภาค", "คะแนนเต็มปลายภาค", "ผลการตัดสินปลายภาค"
  ];
  
  const setupSheet = (s) => {
    s.clear();
    s.appendRow(requiredHeaders);
    s.setFrozenRows(1);
    // Add sample data
    s.appendRow(["1102001234567", "เด็กชายทดสอบ ระบบ", "ป.1", "ภาษาไทย ป.1", 15, 20, "ผ่าน", 10, 20, "ผ่าน"]);
  };

  if (!sheet) {
    sheet = spreadsheet.insertSheet(SHEET_NAME);
    setupSheet(sheet);
  } else {
    const currentHeaders = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    if (JSON.stringify(currentHeaders) !== JSON.stringify(requiredHeaders)) {
      Logger.log("Sheet headers were incorrect. Resetting sheet.");
      setupSheet(sheet);
    }
  }
  return sheet;
}

/**
 * Converts sheet data to an array of objects.
 */
function getSheetDataAsObjects(sheet) {
  const dataRange = sheet.getDataRange();
  if (dataRange.getNumRows() < 2) { return []; }

  const values = dataRange.getValues();
  const headers = values.shift();
  const keyMap = {
    "รหัสประจำตัวประชาชน": "id", "ชื่อ-นามสกุล": "name", "ห้อง/ระดับชั้น": "class", 
    "รายวิชา": "subject", "คะแนนกลางภาค": "midterm_score", "คะแนนเต็มกลางภาค": "midterm_full", 
    "ผลการตัดสินกลางภาค": "midterm_decision", "คะแนนปลายภาค": "final_score", 
    "คะแนนเต็มปลายภาค": "final_full", "ผลการตัดสินปลายภาค": "final_decision"
  };

  return values.map(row => {
    let obj = {};
    headers.forEach((header, index) => {
      const key = keyMap[header] || header.replace(/\s/g, '_');
      obj[key] = row[index];
    });
    return obj;
  });
}

/**
 * Creates a JSON response for the client.
 */
function createJsonResponse(data) {
  return ContentService.createTextOutput(JSON.stringify(data))
    .setMimeType(ContentService.MimeType.JSON);
}