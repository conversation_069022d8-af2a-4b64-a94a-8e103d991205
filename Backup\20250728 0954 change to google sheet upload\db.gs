// === Database Structure & Sample Data ===

/**
 * สร้างชีทหลักและเติมข้อมูลตัวอย่าง
 */
function generateSheetsAndSampleData() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();

  // 1. Course list sheet
  var courseSheet = ss.getSheetByName('course_list') || ss.insertSheet('course_list');
  courseSheet.clear();
  courseSheet.appendRow([
    'รหัสวิชา', 'ชื่อวิชา', 'ประเภทสอบ', 'ปีการศึกษา', 'ภาคการศึกษา', 'อาจารย์ผู้ประกาศ', 'วันที่ประกาศ', 'ชื่อชีตคะแนน'
  ]);
  courseSheet.appendRow(['DEN101', 'Anatomy', 'midterm', '2567', '1', '<EMAIL>', '2024-07-01', 'DEN101_midterm']);
  courseSheet.appendRow(['DEN102', 'Physiology', 'final', '2567', '2', '<EMAIL>', '2024-07-02', 'DEN102_final']);

  // 2. Access log sheet
  var logSheet = ss.getSheetByName('accessLog') || ss.insertSheet('accessLog');
  logSheet.clear();
  logSheet.appendRow(['Timestamp', 'User', 'Email', 'Action', 'Details']);
  logSheet.appendRow([new Date(), 'kunchorn.k', '<EMAIL>', 'เข้าสู่ระบบ', 'ตัวอย่าง log']);

  // 3. Archive sheet
  var archiveSheet = ss.getSheetByName('archive') || ss.insertSheet('archive');
  archiveSheet.clear();
  archiveSheet.appendRow([
    'รหัสวิชา', 'ชื่อวิชา', 'ประเภทสอบ', 'ปีการศึกษา', 'ภาคการศึกษา', 'อาจารย์ผู้ประกาศ', 'วันที่ประกาศ', 'ชื่อชีตคะแนน', 'วันที่ย้ายเข้า archive'
  ]);
  
  // 4. Instructor list sheet
  generateInstructorListSheet();
}

/**
 * สร้างชีท instructor_list และเติมข้อมูลตัวอย่าง
 */
function generateInstructorListSheet() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var instructorSheet = ss.getSheetByName('instructor_list') || ss.insertSheet('instructor_list');
  
  // ล้างข้อมูลเดิม
  instructorSheet.clear();
  
  // สร้าง header
  instructorSheet.appendRow([
    'Email', 'ชื่อ-นามสกุล', 'ตำแหน่ง', 'สิทธิ์การเข้าถึง', 'วันที่เพิ่ม', 'สถานะ'
  ]);
  
  // เพิ่มข้อมูลตัวอย่าง
  instructorSheet.appendRow([
    '<EMAIL>', 
    'กัญจน์ชร กัญจน์ชร', 
    'อาจารย์', 
    'admin', 
    new Date(), 
    'active'
  ]);
  
  instructorSheet.appendRow([
    '<EMAIL>', 
    'อาจารย์ ตัวอย่าง 1', 
    'อาจารย์', 
    'instructor', 
    new Date(), 
    'active'
  ]);
  
  instructorSheet.appendRow([
    '<EMAIL>', 
    'อาจารย์ ตัวอย่าง 2', 
    'อาจารย์', 
    'instructor', 
    new Date(), 
    'active'
  ]);
  
  // ตั้งค่าการจัดรูปแบบ
  var headerRange = instructorSheet.getRange(1, 1, 1, 6);
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#f3f4f6');
  
  // ตั้งค่าความกว้างคอลัมน์
  instructorSheet.setColumnWidth(1, 200); // Email
  instructorSheet.setColumnWidth(2, 150); // ชื่อ-นามสกุล
  instructorSheet.setColumnWidth(3, 100); // ตำแหน่ง
  instructorSheet.setColumnWidth(4, 120); // สิทธิ์การเข้าถึง
  instructorSheet.setColumnWidth(5, 120); // วันที่เพิ่ม
  instructorSheet.setColumnWidth(6, 100); // สถานะ
  
  Logger.log('สร้าง instructor_list sheet สำเร็จ');
}

/**
 * ตรวจสอบว่าผู้ใช้เป็น instructor หรือไม่
 * @param {string} email อีเมลของผู้ใช้
 * @returns {boolean} true ถ้าเป็น instructor
 */
function isInstructor(email) {
  if (!email) return false;
  
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return false;
  
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    var instructorEmail = row[0];
    var status = row[5];
    
    if (instructorEmail.toLowerCase() === email.toLowerCase() && status === 'active') {
      return true;
    }
  }
  
  return false;
}

/**
 * ตรวจสอบว่าผู้ใช้เป็น admin หรือไม่
 * @param {string} email อีเมลของผู้ใช้
 * @returns {boolean} true ถ้าเป็น admin
 */
function isAdmin(email) {
  if (!email) return false;
  
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return false;
  
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    var instructorEmail = row[0];
    var accessLevel = row[3];
    var status = row[5];
    
    if (instructorEmail.toLowerCase() === email.toLowerCase() && 
        accessLevel === 'admin' && 
        status === 'active') {
      return true;
    }
  }
  
  return false;
}

/**
 * ดึงข้อมูล instructor ทั้งหมด
 * @returns {Array} array ของ instructor objects
 */
function getAllInstructors() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return [];
  
  var data = sheet.getDataRange().getValues();
  var instructors = [];
  
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    instructors.push({
      email: row[0],
      name: row[1],
      position: row[2],
      accessLevel: row[3],
      addedDate: row[4],
      status: row[5]
    });
  }
  
  return instructors;
}

/**
 * เพิ่ม instructor ใหม่
 * @param {Object} instructor ข้อมูล instructor
 */
function addInstructor(instructor) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) {
    generateInstructorListSheet();
    sheet = ss.getSheetByName('instructor_list');
  }
  
  sheet.appendRow([
    instructor.email,
    instructor.name,
    instructor.position || 'อาจารย์',
    instructor.accessLevel || 'instructor',
    new Date(),
    'active'
  ]);
  
  Logger.log('เพิ่ม instructor: ' + instructor.email);
}

/**
 * อัปเดตสถานะ instructor
 * @param {string} email อีเมลของ instructor
 * @param {string} status สถานะใหม่ (active/inactive)
 */
function updateInstructorStatus(email, status) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('instructor_list');
  if (!sheet) return;
  
  var data = sheet.getDataRange().getValues();
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    if (row[0].toLowerCase() === email.toLowerCase()) {
      sheet.getRange(i + 1, 6).setValue(status);
      Logger.log('อัปเดตสถานะ instructor: ' + email + ' เป็น ' + status);
      break;
    }
  }
}

/**
 * ดึงรายวิชาที่อาจารย์เป็นผู้ประกาศ
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @returns {Array} รายวิชาที่อาจารย์เป็นผู้ประกาศ
 */
function getInstructorCourses(instructorEmail) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var courseSheet = ss.getSheetByName('course_list');
  if (!courseSheet) return [];
  
  var data = courseSheet.getDataRange().getValues();
  var courses = [];
  
  for (var i = 1; i < data.length; i++) {
    var row = data[i];
    var teacherEmail = row[5];
    
    if (teacherEmail.toLowerCase() === instructorEmail.toLowerCase()) {
      courses.push({
        code: row[0],
        name: row[1],
        examType: row[2],
        year: row[3],
        term: row[4],
        teacher: row[5],
        announceDate: (row[6] instanceof Date) ? row[6].toISOString() : String(row[6]),
        scoreSheetName: row[7]
      });
    }
  }
  
  return courses;
}

/**
 * ค้นหาคะแนนนักศึกษาตามคำค้นหา (เฉพาะรายวิชาที่อาจารย์เป็นผู้ประกาศ)
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @param {string} searchTerm คำค้นหา (ID, ชื่อ, นามสกุล)
 * @returns {Array} ผลการค้นหาคะแนน
 */
function searchStudentScoresByInstructor(instructorEmail, searchTerm) {
  if (!searchTerm || searchTerm.trim() === '') {
    return [];
  }
  
  var searchLower = searchTerm.toLowerCase().trim();
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var courseSheet = ss.getSheetByName('course_list');
  var results = [];
  
  if (!courseSheet) return results;
  
  var courseData = courseSheet.getDataRange().getValues();
  
  // หารายวิชาที่อาจารย์เป็นผู้ประกาศ
  for (var i = 1; i < courseData.length; i++) {
    var courseRow = courseData[i];
    var teacherEmail = courseRow[5];
    
    if (teacherEmail.toLowerCase() === instructorEmail.toLowerCase()) {
      var scoreSheetName = courseRow[7];
      var scoreSheet = ss.getSheetByName(scoreSheetName);
      
      if (scoreSheet) {
        var scoreData = scoreSheet.getDataRange().getValues();
        var headers = scoreData[0];
        
        // หา index ของ section ทั้งหมด
        var sectionIndexes = [];
        for (var j = 3; j < headers.length - 1; j++) {
          var h1 = (headers[j] || '').toString();
          var h2 = (headers[j+1] || '').toString();
          if (h1.includes('score') && h2.includes('full score')) {
            var sectionName = h1.replace('score', '').trim() || 'Section ' + (sectionIndexes.length + 1);
            sectionIndexes.push({
              section: sectionName,
              scoreIdx: j,
              fullScoreIdx: j+1
            });
            j++; // skip next (full score)
          }
        }
        
        // ค้นหาในแต่ละแถวของชีทคะแนน
        for (var k = 1; k < scoreData.length; k++) {
          var scoreRow = scoreData[k];
          var studentId = String(scoreRow[0] || '');
          var studentName = String(scoreRow[1] || '');
          
          // ตรวจสอบว่าตรงกับคำค้นหาหรือไม่
          if (studentId.toLowerCase().includes(searchLower) || 
              studentName.toLowerCase().includes(searchLower)) {
            
            // สร้าง sections array
            var sections = sectionIndexes.map(function(sec) {
              return {
                section: sec.section,
                score: scoreRow[sec.scoreIdx],
                fullScore: scoreRow[sec.fullScoreIdx]
              };
            });
            
            // คำนวณคะแนนรวม (สอง column สุดท้าย)
            var totalScore = scoreRow[scoreRow.length - 2];
            var totalFull = scoreRow[scoreRow.length - 1];
            
            results.push({
              courseCode: courseRow[0],
              courseName: courseRow[1],
              examType: courseRow[2],
              year: courseRow[3],
              term: courseRow[4],
              announceDate: (courseRow[6] instanceof Date) ? courseRow[6].toISOString() : String(courseRow[6]),
              scoreSheetName: scoreSheetName,
              studentId: studentId,
              studentName: studentName,
              studentEmail: scoreRow[2],
              sections: sections,
              totalScore: totalScore,
              totalFull: totalFull
            });
          }
        }
      }
    }
  }
  
  return results;
}

/**
 * สร้าง archive sheet ใน spreadsheet แยก (archiveLog)
 */
function setupArchiveSheet() {
  var archiveSS = SpreadsheetApp.openById('1iN2pNyXKvzoXb-lxAiR2aHyZ-LQbwDugZIyiC51CEr0');
  var archiveSheet = archiveSS.getSheetByName('archiveLog') || archiveSS.insertSheet('archiveLog');
  archiveSheet.clear();
  archiveSheet.appendRow([
    'รหัสวิชา', 'ชื่อวิชา', 'ประเภทสอบ', 'ปีการศึกษา', 'ภาคการศึกษา', 'อาจารย์ผู้ประกาศ', 'วันที่ประกาศ', 'ชื่อชีตคะแนน', 'วันที่ย้ายเข้า archive'
  ]);
}

/**
 * สร้างชีทคะแนนรายวิชาตัวอย่าง
 * @param {string} sheetName ชื่อชีทคะแนน เช่น DEN101_midterm
 */
function generateSampleCourseScoreSheet(sheetName) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var scoreSheet = ss.getSheetByName(sheetName) || ss.insertSheet(sheetName);
  scoreSheet.clear();
  scoreSheet.appendRow([
    'student ID', 'student name', 'student email', 'score', 'full score'
  ]);
  scoreSheet.appendRow(['7000001', 'สมชาย ใจดี', '<EMAIL>', 78, 100]);
  scoreSheet.appendRow(['7000002', 'สมหญิง เก่งมาก', '<EMAIL>', 85, 100]);
  scoreSheet.appendRow(['7000003', 'สมปอง สู้ๆ', '<EMAIL>', 92, 100]);
}

/**
 * ดึงข้อมูลคะแนนจากชีทคะแนนรายวิชา
 * @param {string} sheetName
 * @returns {Array} array of score objects
 */
function getSampleCourseScores(sheetName) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName(sheetName);
  if (!sheet) return [];
  var data = sheet.getDataRange().getValues();
  return data.slice(1).map(function(row) {
    return {
      studentId: row[0],
      studentName: row[1],
      studentEmail: row[2],
      score: row[3],
      fullScore: row[4]
    };
  });
}

// === Data Validation ===
function isValidStudentId(id) {
  return /^\d{7}$/.test(id);
}

function isValidEmail(email) {
  return /@rsu\.ac\.th$/.test(email);
}

// === Database Helper Functions (CRUD) ===
function addCourse(course) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('course_list');
  sheet.appendRow([
    course.code, course.name, course.examType, course.year, course.term, course.teacher, course.announceDate, course.scoreSheetName
  ]);
}

function getCourses() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('course_list');
  var data = sheet.getDataRange().getValues();
  return data.slice(1).map(function(row) {
    return {
      code: row[0], name: row[1], examType: row[2], year: row[3], term: row[4], teacher: row[5], announceDate: row[6], scoreSheetName: row[7]
    };
  });
}

function logAccess(user, email, action, details) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName('accessLog');
  sheet.appendRow([new Date(), user, email, action, details]);
}

/**
 * ดึงรายวิชาทั้งหมดที่มีคะแนนของ email นี้
 * @param {string} email
 * @returns {Array} รายวิชาที่มีคะแนนของ email นี้
 */
function getStudentCourses() {
  var email = Session.getActiveUser().getEmail();
  if (!email || typeof email !== 'string' || email.trim() === '') {
    Logger.log('ERROR: No active user email');
    return [];
  }
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var courseSheet = ss.getSheetByName('course_list');
  var courses = courseSheet.getDataRange().getValues().slice(1);
  var result = [];
  Logger.log('=== DEBUG getStudentCourses ===');
  Logger.log('Target email: ' + email);
  courses.forEach(function(row) {
    var sheetName = row[7];
    Logger.log('Checking sheet: ' + sheetName);
    var scoreSheet = ss.getSheetByName(sheetName);
    if (!scoreSheet) {
      Logger.log('Sheet not found: ' + sheetName);
      return;
    }
    var data = scoreSheet.getDataRange().getValues();
    for (var i = 1; i < data.length; i++) {
      Logger.log('Row ' + i + ' email: ' + data[i][2]);
      if ((data[i][2] || '').toLowerCase() === email.toLowerCase()) {
        Logger.log('MATCH: ' + data[i][2]);
        result.push({
          code: row[0],
          name: row[1],
          examType: row[2],
          year: row[3],
          term: row[4],
          teacher: row[5],
          announceDate: (row[6] instanceof Date) ? row[6].toISOString() : String(row[6]),
          scoreSheetName: row[7]
        });
        break;
      }
    }
  });
  Logger.log('Result count: ' + result.length);
  return result;
}

/**
 * ดึงคะแนนและ stat ของนักศึกษาคนนี้ในรายวิชานั้น ๆ
 * @param {string} email
 * @param {string} sheetName
 * @returns {Object} { scores: [...], stats: {...} }
 */
function getStudentCourseScores(sheetName) {
  var email = Session.getActiveUser().getEmail();
  if (!email || typeof email !== 'string' || email.trim() === '') {
    Logger.log('ERROR: No active user email');
    return { scores: [], stats: {} };
  }
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName(sheetName);
  if (!sheet) return { scores: [], stats: {} };
  var data = sheet.getDataRange().getValues();
  var headers = data[0];
  // หา index ของ section ทั้งหมด
  var sectionIndexes = [];
  for (var i = 3; i < headers.length - 1; i++) {
    var h1 = (headers[i] || '').toString();
    var h2 = (headers[i+1] || '').toString();
    if (h1.includes('score') && h2.includes('full score')) {
      // หาชื่อ section เช่น "Section A" จากชื่อ header
      var sectionName = h1.replace('score', '').trim() || 'Section ' + (sectionIndexes.length + 1);
      sectionIndexes.push({
        section: sectionName,
        scoreIdx: i,
        fullScoreIdx: i+1
      });
      i++; // skip next (full score)
    }
  }
  // สถิติคิดจากคะแนนรวม (สอง column สุดท้าย)
  var allScores = [];
  for (var r = 1; r < data.length; r++) {
    var v = data[r][data[0].length - 2]; // col รองสุดท้าย = คะแนนที่ได้
    if (typeof v === 'number' || (!isNaN(parseFloat(v)) && isFinite(v))) {
      allScores.push(Number(v));
    }
  }
  var studentRows = data.slice(1).filter(function(row) { return (row[2] || '').toLowerCase() === email.toLowerCase(); });
  var stats = calculateStats(allScores);
  // หา rank และ percentile ของนักศึกษาคนนี้ (คะแนนรวม col รองสุดท้าย)
  var studentScore = null;
  if (studentRows.length > 0) {
    var v = studentRows[0][data[0].length - 2];
    if (typeof v === 'number' || (!isNaN(parseFloat(v)) && isFinite(v))) {
      studentScore = Number(v);
    }
  }
  if (studentScore !== null) {
    var sorted = allScores.slice().sort(function(a, b) { return b - a; });
    var rank = sorted.indexOf(studentScore) + 1;
    var percentile = Math.round((sorted.length - rank) / sorted.length * 100);
    stats.rank = rank;
    stats.percentile = percentile;
    stats.total = stats.total || sorted.length;
  }
  return {
    scores: studentRows.map(function(row) {
      var sections = sectionIndexes.map(function(sec) {
        return {
          section: sec.section,
          score: row[sec.scoreIdx],
          fullScore: row[sec.fullScoreIdx]
        };
      });
      return {
        studentId: row[0],
        studentName: row[1],
        studentEmail: row[2],
        sections: sections
      };
    }),
    stats: stats,
    sectionNames: sectionIndexes.map(function(sec) { return sec.section; })
  };
}

/**
 * คำนวณ Mean, SD, Min, Max
 * @param {Array} scores (number[])
 * @returns {Object}
 */
function calculateStats(scores) {
  if (!scores || scores.length === 0) return {};
  var n = scores.length;
  var mean = scores.reduce(function(a, b) { return a + Number(b); }, 0) / n;
  var sd = Math.sqrt(scores.map(function(x) { return Math.pow(Number(x) - mean, 2); }).reduce(function(a, b) { return a + b; }, 0) / n);
  var min = Math.min.apply(null, scores);
  var max = Math.max.apply(null, scores);
  return { mean: mean, sd: sd, min: min, max: max, count: n, total: n };
} 

/**
 * สร้าง template สำหรับกรอกคะแนน
 * @returns {Object} ข้อมูล template
 */
function generateScoreTemplate() {
  var template = {
    headers: ['student ID', 'student name', 'student email', 'score', 'full score'],
    sampleData: [
      ['7000001', 'สมชาย ใจดี', '<EMAIL>', 78, 100],
      ['7000002', 'สมหญิง เก่งมาก', '<EMAIL>', 85, 100],
      ['7000003', 'สมปอง สู้ๆ', '<EMAIL>', 92, 100]
    ],
    instructions: [
      '1. กรอกข้อมูลนักศึกษาในคอลัมน์ student ID, student name, student email',
      '2. กรอกคะแนนที่ได้และคะแนนเต็มในคอลัมน์ที่ 4 และ 5',
      '3. หากมีหลาย section ให้เพิ่มคอลัมน์คู่: "score Section A", "full score Section A", "score Section B", "full score Section B" ตามลำดับ',
      '4. ตรวจสอบให้แน่ใจว่า student ID เป็นเลข 7 หลัก และ email เป็น @rsu.ac.th',
      '5. บันทึกไฟล์เป็น CSV หรือ Excel (.xlsx)',
      '6. หากมีปัญหาเรื่องภาษาไทย ให้เปิดไฟล์ใน Excel และบันทึกเป็น UTF-8 CSV',
      '7. ระบบจะคำนวณคะแนนรวมและสถิติอัตโนมัติ'
    ]
  };
  
  return template;
}

/**
 * สร้าง template สำหรับหลาย section
 * @param {number} numSections จำนวน section
 * @returns {Object} ข้อมูล template
 */
function generateMultiSectionTemplate(numSections) {
  var headers = ['student ID', 'student name', 'student email'];
  var sampleData = [
    ['7000001', 'สมชาย ใจดี', '<EMAIL>'],
    ['7000002', 'สมหญิง เก่งมาก', '<EMAIL>'],
    ['7000003', 'สมปอง สู้ๆ', '<EMAIL>']
  ];
  
  // เพิ่ม headers สำหรับแต่ละ section
  for (var i = 1; i <= numSections; i++) {
    headers.push('score Section ' + String.fromCharCode(64 + i)); // A, B, C, ...
    headers.push('full score Section ' + String.fromCharCode(64 + i));
  }
  
  // เพิ่มข้อมูลตัวอย่าง
  for (var i = 0; i < sampleData.length; i++) {
    for (var j = 1; j <= numSections; j++) {
      sampleData[i].push(Math.floor(Math.random() * 30) + 70); // คะแนน 70-100
      sampleData[i].push(100); // คะแนนเต็ม
    }
  }
  
  return {
    headers: headers,
    sampleData: sampleData,
    instructions: [
      '1. กรอกข้อมูลนักศึกษาในคอลัมน์ student ID, student name, student email',
      '2. กรอกคะแนนสำหรับแต่ละ section ในคอลัมน์คู่',
      '3. ระบบจะคำนวณคะแนนรวมและสถิติอัตโนมัติ',
      '4. ตรวจสอบให้แน่ใจว่า student ID เป็นเลข 7 หลัก และ email เป็น @rsu.ac.th'
    ]
  };
}

/**
 * สร้าง CSV content สำหรับ template พร้อม UTF-8 BOM
 * @returns {string} CSV content
 */
function generateCSVTemplate() {
  var template = generateScoreTemplate();
  var csvContent = '\uFEFF'; // UTF-8 BOM
  
  // Headers
  csvContent += template.headers.join(',') + '\n';
  
  // Sample data
  template.sampleData.forEach(function(row) {
    csvContent += row.join(',') + '\n';
  });
  
  return csvContent;
}

/**
 * ประมวลผลการอัปโหลดคะแนน
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @param {Object} uploadData ข้อมูลการอัปโหลด
 * @returns {Object} ผลการประมวลผล
 */
function processScoreUpload(instructorEmail, uploadData) {
  try {
    // ตรวจสอบข้อมูลที่จำเป็น
    if (!uploadData.courseCode || !uploadData.courseName || !uploadData.examType || 
        !uploadData.year || !uploadData.term || !uploadData.scoreData) {
      return { error: 'ข้อมูลไม่ครบถ้วน' };
    }
    
    // ทำความสะอาดข้อมูล
    var cleanedResult = cleanScoreData(uploadData.scoreData);
    if (cleanedResult.error) {
      return { error: 'ข้อมูลไม่ถูกต้อง: ' + cleanedResult.error };
    }
    
    var cleanedData = cleanedResult.data;
    
    // Debug: แสดงข้อมูลที่ทำความสะอาดแล้ว
    Logger.log('DEBUG: Cleaned data = ' + JSON.stringify(cleanedData));
    
    // ตรวจสอบรูปแบบข้อมูล
    var validation = validateScoreData(cleanedData);
    if (!validation.isValid) {
      return { error: 'ข้อมูลไม่ถูกต้อง: ' + validation.errors.join(', ') };
    }
    
    // สร้างชื่อชีตคะแนนที่ไม่ซ้ำ
    var baseSheetName = uploadData.courseCode + '_' + uploadData.examType + '_' + uploadData.year + '_' + uploadData.term;
    var scoreSheetName = generateUniqueSheetName(baseSheetName);
    
    // สร้างชีตคะแนนใหม่
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var scoreSheet = ss.insertSheet(scoreSheetName);
    
    // เขียนข้อมูลคะแนน
    var headers = cleanedData[0];
    var numCols = headers.length;
    
    // เขียน headers
    scoreSheet.getRange(1, 1, 1, numCols).setValues([headers]);
    
    // เขียนข้อมูลคะแนน
    if (cleanedData.length > 1) {
      scoreSheet.getRange(2, 1, cleanedData.length - 1, numCols)
        .setValues(cleanedData.slice(1));
    }
    
    // คำนวณและเพิ่มสถิติ
    addStatisticsToSheet(scoreSheet, headers);
    
    // เพิ่มข้อมูลลงใน course_list
    var courseData = [
      uploadData.courseCode,
      uploadData.courseName,
      uploadData.examType,
      uploadData.year,
      uploadData.term,
      instructorEmail,
      new Date(),
      scoreSheetName
    ];
    
    var courseSheet = ss.getSheetByName('course_list');
    courseSheet.appendRow(courseData);
    
    return { 
      success: true, 
      message: 'อัปโหลดคะแนนสำเร็จ',
      scoreSheetName: scoreSheetName
    };
    
  } catch (e) {
    Logger.log('processScoreUpload ERROR: ' + e);
    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
}

/**
 * ตรวจสอบความถูกต้องของข้อมูลคะแนน
 * @param {Array} scoreData ข้อมูลคะแนน
 * @returns {Object} ผลการตรวจสอบ
 */
function validateScoreData(scoreData) {
  var errors = [];
  
  if (!scoreData || scoreData.length < 2) {
    errors.push('ไม่มีข้อมูลคะแนน');
    return { isValid: false, errors: errors };
  }
  
  var headers = scoreData[0];
  var numCols = headers.length;
  
  // ตรวจสอบจำนวนคอลัมน์ขั้นต่ำ
  if (numCols < 5) {
    errors.push('ข้อมูลต้องมีอย่างน้อย 5 คอลัมน์ (student ID, student name, student email, score, full score)');
    return { isValid: false, errors: errors };
  }
  
  // ตรวจสอบข้อมูลในแต่ละแถว
  for (var r = 1; r < scoreData.length; r++) {
    var row = scoreData[r];
    var rowNum = r + 1;
    
    // ตรวจสอบ student ID (คอลัมน์ที่ 1)
    var studentId = String(row[0] || '');
    if (!isValidStudentId(studentId)) {
      errors.push('แถว ' + rowNum + ': Student ID ไม่ถูกต้อง (ต้องเป็นเลข 7 หลัก)');
    }
    
    // ตรวจสอบ email (คอลัมน์ที่ 3)
    var email = String(row[2] || '');
    if (!isValidEmail(email)) {
      errors.push('แถว ' + rowNum + ': Email ไม่ถูกต้อง (ต้องเป็น @rsu.ac.th)');
    }
    
    // ตรวจสอบคะแนน (เริ่มจากคอลัมน์ที่ 4)
    for (var c = 3; c < numCols; c++) {
      var score = row[c];
      if (c % 2 === 1) { // score (คอลัมน์คี่)
        if (isNaN(parseFloat(score)) || parseFloat(score) < 0) {
          errors.push('แถว ' + rowNum + ' คอลัมน์ ' + (c + 1) + ': คะแนนไม่ถูกต้อง');
        }
      } else { // full score (คอลัมน์คู่)
        if (isNaN(parseFloat(score)) || parseFloat(score) <= 0) {
          errors.push('แถว ' + rowNum + ' คอลัมน์ ' + (c + 1) + ': คะแนนเต็มไม่ถูกต้อง');
        }
      }
    }
  }
  
  return { isValid: errors.length === 0, errors: errors };
}

/**
 * เพิ่มสถิติลงในชีตคะแนน
 * @param {Sheet} scoreSheet ชีตคะแนน
 * @param {Array} headers headers ของชีต
 */
function addStatisticsToSheet(scoreSheet, headers) {
  var data = scoreSheet.getDataRange().getValues();
  var numCols = headers.length;
  
  // หา index ของคอลัมน์คะแนนโดยใช้ column index
  var scoreColIndex = 3; // คอลัมน์ที่ 4 (index 3)
  var fullScoreColIndex = 4; // คอลัมน์ที่ 5 (index 4)
  
  // ตรวจสอบว่ามีคอลัมน์คะแนนหรือไม่
  if (numCols <= scoreColIndex) {
    Logger.log('ERROR: ไม่มีคอลัมน์คะแนนเพียงพอ');
    return;
  }
  
  // คำนวณคะแนนรวมสำหรับแต่ละแถว
  var totalScores = [];
  for (var r = 1; r < data.length; r++) {
    var row = data[r];
    var score = 0;
    var fullScore = 0;
    
    // กรณีมีหลาย section (คอลัมน์คู่)
    if (numCols > 5) {
      // รวมคะแนนจากทุก section (เริ่มจากคอลัมน์ที่ 4)
      for (var c = 3; c < numCols - 1; c += 2) {
        var sectionScore = parseFloat(row[c]) || 0;
        var sectionFullScore = parseFloat(row[c + 1]) || 0;
        score += sectionScore;
        fullScore += sectionFullScore;
      }
    } else {
      // กรณีมีแค่คอลัมน์เดียว
      score = parseFloat(row[scoreColIndex]) || 0;
      fullScore = parseFloat(row[fullScoreColIndex]) || 0;
    }
    
    totalScores.push(score);
    
    // เพิ่มคอลัมน์คะแนนรวมถ้ายังไม่มี
    if (numCols <= 5) {
      // เพิ่มคอลัมน์คะแนนรวม
      scoreSheet.getRange(1, numCols + 1).setValue('Total Score');
      scoreSheet.getRange(1, numCols + 2).setValue('Total Full Score');
      scoreSheet.getRange(r + 1, numCols + 1).setValue(score);
      scoreSheet.getRange(r + 1, numCols + 2).setValue(fullScore);
    }
  }
  
  // คำนวณสถิติ
  var stats = calculateStats(totalScores);
  
  // เพิ่มแถวสถิติ
  var statsRow = data.length + 1;
  var statsCols = Math.max(6, numCols);
  
  scoreSheet.getRange(statsRow, 1).setValue('สถิติ');
  scoreSheet.getRange(statsRow, 2).setValue('Mean: ' + stats.mean.toFixed(2));
  scoreSheet.getRange(statsRow, 3).setValue('SD: ' + stats.sd.toFixed(2));
  scoreSheet.getRange(statsRow, 4).setValue('Min: ' + stats.min);
  scoreSheet.getRange(statsRow, 5).setValue('Max: ' + stats.max);
  scoreSheet.getRange(statsRow, 6).setValue('จำนวน: ' + stats.count);
  
  // จัดรูปแบบแถวสถิติ
  var statsRange = scoreSheet.getRange(statsRow, 1, 1, statsCols);
  statsRange.setFontWeight('bold');
  statsRange.setBackground('#f3f4f6');
}

/**
 * จัดการการประกาศคะแนน
 * @param {string} instructorEmail อีเมลของอาจารย์
 * @param {string} action toggle หรือ delete
 * @param {string} scoreSheetName ชื่อชีตคะแนน
 * @returns {Object} ผลการจัดการ
 */
function manageScoreAnnouncement(instructorEmail, action, scoreSheetName) {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var courseSheet = ss.getSheetByName('course_list');
    var courseData = courseSheet.getDataRange().getValues();
    
    // หาแถวของรายวิชานี้
    var targetRow = -1;
    for (var i = 1; i < courseData.length; i++) {
      var row = courseData[i];
      if (row[7] === scoreSheetName && row[5] === instructorEmail) {
        targetRow = i + 1;
        break;
      }
    }
    
    if (targetRow === -1) {
      return { error: 'ไม่พบรายวิชานี้หรือไม่มีสิทธิ์จัดการ' };
    }
    
    if (action === 'delete') {
      // ลบจาก course_list
      courseSheet.deleteRow(targetRow);
      
      // ลบชีตคะแนน
      var scoreSheet = ss.getSheetByName(scoreSheetName);
      if (scoreSheet) {
        ss.deleteSheet(scoreSheet);
      }
      
      return { success: true, message: 'ลบการประกาศคะแนนสำเร็จ' };
      
    } else if (action === 'toggle') {
      // Toggle สถานะการประกาศ (เพิ่มคอลัมน์ status ถ้ายังไม่มี)
      var headers = courseData[0];
      var statusCol = headers.indexOf('สถานะ');
      
      if (statusCol === -1) {
        // เพิ่มคอลัมน์ status
        courseSheet.getRange(1, headers.length + 1).setValue('สถานะ');
        statusCol = headers.length;
      }
      
      var currentStatus = courseSheet.getRange(targetRow, statusCol + 1).getValue();
      var newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      courseSheet.getRange(targetRow, statusCol + 1).setValue(newStatus);
      
      return { 
        success: true, 
        message: 'เปลี่ยนสถานะการประกาศเป็น: ' + newStatus,
        newStatus: newStatus
      };
    }
    
    return { error: 'การดำเนินการไม่ถูกต้อง' };
    
  } catch (e) {
    Logger.log('manageScoreAnnouncement ERROR: ' + e);
    return { error: 'เกิดข้อผิดพลาด: ' + e.toString() };
  }
} 