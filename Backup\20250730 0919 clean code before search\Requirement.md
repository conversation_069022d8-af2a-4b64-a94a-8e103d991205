ScaryScore
ระบบประกาศคะแนน Dent RSU
College of Dental Medicine, Rangsit University
- พัฒนาบน google app script และ google sheet ไม่มีโครงสร้าง folder
- tailwind css, responsive, mobile first เน้นการเข้าใช้งานจาก โทรศัพท์มือถือ,ใช้ card design
- ใช้ UI รูปแบบเดียวกับ Example code.js และ Example Index.html จาก folder Examples
- Icon font awesome
- ใช้ google account @rsu.ac.th เป็น google workspace function ที่เกี่ยวข้องกับการเรียกข้อมูลให้ใช้  Session.getActiveUser().getEmail() 
- admin, instructor: <EMAIL>
- sheet ID: 1Z8w5GUIBhTfZqBthxccQxFD9k0DXv40kiSg_tNxzdUc
- archive sheet ID: 1iN2pNyXKvzoXb-lxAiR2aHyZ-LQbwDugZIyiC51CEr0
- Student ID เป็นรูปแบบ เลข 7 ตัว เช่น 6xxxxxx หรือ 7xxxxxx (ขึ้นต้นด้วย พศ.)
- เขียน code แยกเป็นไฟล์ตามกลุ่ม function 

webapp จะแบ่งเป็น 2 ส่วนคือส่วนของนักศึกษาและส่วนของอาจารย์

เมื่อเปิดหน้า webapp จะทำการตรวจสอบ google account ที่ใช้ Session.getActiveUser 
จะต้องเป็นของ rsu เท่านั้น หากไม่ใช่ให้ปฏิเสธการเข้าใช้งาน แจ้งให้ login ด้วย google account @rsu.ac.th
ใช้หลักการเดียวกับโค้ดตัวอย่าง  Example code.js และ Example Index.html จาก folder Examples

เมื่อเข้าสู้หน้าจอหลักของนักศึกษา จะแสดง google account ที่ใช้อยู่ และแสดงทุกรายวิชาที่มีการประกาศคะแนน ที่มี email google account ของนักศึกษาที่เข้าใช้งาน  โดยมีรายละเอียด คือรหัสวิชา ชื่อวิชา วันที่ประกาศคะแนน เรียงตามรายวิชาล่าสุดที่มีการประกาศ

เมื่อคลิกที่รายวิชา เพื่อดูคะแนน จะปรากฏคะแนน, คะแนนเต็ม, ของคะแนนทุก section ที่อยู่ใน sheet (กรณีที่มีหลาย section) และแสดง stat. แต่หากมีคะแนนหลาย section ให้คิด stat. เฉพาะของคะแนนรวม

หาก google account ที่เข้าใช้งานเป็นอาจารย์ หรืออยู่ใน sheet instructor list หน้า main ของ webapp มุมบนขวา จะมีให้คลิก instructor mode (หากเป็น นศ จะไม่มี ปุ่มนี้) เพื่อนำไปสู่หน้าการอัพโหลดคะแนน  webapp ส่วน instructor 

โดยโครงสร้างของข้อมูลคะแนน
ประกอบด้วย
1. ชีต course list
โครงสร้าง column ประกอบด้วย 
รหัสวิชา, ชื่อวิชา, ระบุประเภทของการสอบ midterm, final, อื่นๆ ให้ระบุ เช่น quiz,
ปีการศึกษา (ใน input ขึ้น default เป็นปีปัจจุบัน), ภาคการศึกษา S, 1, 2 ขึ้นอัตโนมัติตามเดือน ปัจจุบัน ว่าอยู่ในเทอมไหน ดังนี้ S: มิย - กค, 1: สค - ธค, 2: มค - เมย,
อาจารย์ผู้ประกาศ (อัพโหลด) ใช้เป็น email google account, วันที่ประกาศ, ชื่อชีตคะแนน (ตั้งชื่อให้สอดคล้องกับการสอบและเช็คได้เมื่อเห็นชื่อ) ของการสอบนี้


2. ชีตคะแนน รายวิชา
โครงสร้าง column ประกอบด้วย 
student ID, student name, student email, คะแนนที่ได้, คะแนนเต็ม อาจมีหลาย section เช่น section A คะแนนที่ได้,section A คะแนนเต็ม,...  section B คะแนนที่ได้,section B คะแนนเต็ม...ให้ประกาศทุก column ที่อยู่ใน sheet นั้น

หน้า webapp ส่วน instructor จะมีให้ download template เพื่อให้อาจารย์นำไปใช้ในการกรอกคะแนน และมีปุ่มให้อัพโหลดไฟล์คะแนนที่กรอกเสร็จแล้ว โดยมี input ให้อาจารย์กรอกรายละเอียดตามโครงสร้างของชีตคะแนน มี checkbox ให้อาจารย์เลือกว่าจะแสดง stat.ของการสอบ อะไรบ้าง ในการประกาศคะแนน เช่น Mean, SD, Min, Max, Percentile, Rank อันดับในกลุ่มผู้เรียน 

เมื่ออัพโหลด ให้มีการทำ Data validation ตรวจสอบข้อมูลที่อัปโหลดเข้ามา แจ้ง error หากมีความผิดพลาด  ถ้าถูกต้องให้เขียนข้อมูลลงใน sheet course  list และสร้างชีทคะแนนรายวิชานั้นๆ คำนวณและบันทึกค่า stat. การสอบของ นศ แต่ละคน ต่อจาก column ท้ายสุดไว้เลย เพื่อจะได้ไม่ต้องคำนวณขณะที่ นศ เรียกดู ได้แก่ Mean, SD, Min, Max, Percentile, Rank อันดับในกลุ่มผู้เรียน เพื่อให้พร้อมกับการประกาศคะแนนและเรียกดูได้อย่างรวดเร็ว

นอกจากนี้ยังมี interface ให้อาจารย์จัดการ คะแนนที่อัพโหลดเข้ามาแล้ว toggle การประกาศ ลบการประกาศเป็นต้น
ถ้าเป็น admin user จะจัดการได้ทุกรายวิชา แต่ถ้าเป็นอาจารย์ทั่วไป จะจัดการเฉพาะรายวิชาที่ตนเป็นผู้อัพโหลดคะแนนเข้ามาเท่านั้น

สำหรับอาจารย์ที่เป็นผู้อัพโหลดคะแนนในรายวิชานั้นๆเมื่อเปิดเว็บ app จะสามารถพิมพ์ ID ของนักศึกษาเพื่อเข้าดูการประกาศคะแนนของแต่ละคนได้ แต่จะดูได้เฉพาะคะแนนรายวิชาที่อาจารย์ท่านนั้นๆเป็นผู้ประกาศเท่านั้น

การจัดการข้อมูลแบบอัตโนมัติคือ การประกาศคะแนนจะจะแสดงผลอยู่ 30 วันหลังจากการประกาศเท่านั้น (มีแจ้งไว้ใน interface ให้ทั้งอาจารย์ และนักศึกษาทราบ) เมื่อครบ 30 วัน ชีตคะแนน จะถูกย้ายไปไว้ใน archive sheet ในอีก spreadsheet ID เลย เพื่อให้ มี sheet คะแนน ในฐานข้อมูลหลักไม่เยอะเกินไป โดยนำข้อมูลจากชีต course list ไป append ต่อ last row ไว้ด้วยด้วย และใน sheet หลัก ก็ให้ลบ row ของรายวิชาที่ moved to archive ออก

มี sheet เก็บ log การเข้าใช้งานด้วย เอาไว้ที่ main spreadsheet เลย ชื่อ accessLog
เขียน function gen sheet ที่เกี่ยวข้องและ ตัวอย่างข้อมูลให้พร้อม test