function showError(message) {
  return { status: 'error', message: message };
}

function showSuccess(message, data) {
  return { status: 'success', message: message, data: data };
}

/**
 * ประมวลผลไฟล์ CSV
 * @deprecated ฟังก์ชันนี้ไม่ได้ใช้แล้ว เนื่องจากใช้ PapaParse ในฝั่ง client แทน
 * @param {string} csvContent เนื้อหาไฟล์ CSV
 * @returns {Array} array ของข้อมูล
 */
function parseCSV(csvContent) {
  try {
    // ลบ UTF-8 BOM ถ้ามี
    if (csvContent.charCodeAt(0) === 0xFEFF) {
      csvContent = csvContent.slice(1);
    }
    
    // แยกเป็นบรรทัดและกรองบรรทัดว่าง
    const lines = csvContent.split('\n').filter(line => line.trim() !== '');
    
    return lines.map(line => {
      // แยกด้วย comma แต่ไม่แยกถ้า comma อยู่ใน quotes
      const cells = [];
      let current = '';
      let inQuotes = false;
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          cells.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      cells.push(current.trim());
      
      return cells;
    });
  } catch (e) {
    Logger.log('parseCSV ERROR: ' + e);
    return [];
  }
}

/**
 * ตรวจสอบรูปแบบไฟล์
 * @param {string} fileName ชื่อไฟล์
 * @returns {boolean} true ถ้าเป็นไฟล์ที่รองรับ
 */
function isValidFileType(fileName) {
  const validExtensions = ['.csv', '.xlsx', '.xls'];
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return validExtensions.includes(extension);
}

/**
 * สร้างชื่อชีตคะแนนที่ไม่ซ้ำ
 * @param {string} baseName ชื่อพื้นฐาน
 * @returns {string} ชื่อชีตที่ไม่ซ้ำ
 */
function generateUniqueSheetName(baseName) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  let counter = 1;
  let sheetName = baseName;
  
  while (ss.getSheetByName(sheetName)) {
    sheetName = baseName + '_' + counter;
    counter++;
  }
  
  return sheetName;
}

/**
 * ตรวจสอบและทำความสะอาดข้อมูลคะแนน
 * @param {Array} scoreData ข้อมูลคะแนน
 * @returns {Object} ข้อมูลที่ทำความสะอาดแล้ว
 */
function cleanScoreData(scoreData) {
  if (!scoreData || scoreData.length < 2) {
    return { error: 'ไม่มีข้อมูลคะแนน' };
  }
  
  const cleanedData = [];
  const errors = [];
  
  // ทำความสะอาด headers
  const headers = scoreData[0].map(header => {
    let cleanHeader = header.trim();
    // ลบ quotes และ whitespace ที่ไม่จำเป็น
    if (cleanHeader.startsWith('"') && cleanHeader.endsWith('"')) {
      cleanHeader = cleanHeader.slice(1, -1);
    }
    return cleanHeader;
  });
  cleanedData.push(headers);
  
  // ทำความสะอาดข้อมูล
  for (let i = 1; i < scoreData.length; i++) {
    const row = scoreData[i];
    const cleanedRow = [];

    for (let j = 0; j < row.length; j++) {
      let cell = row[j];

      // ทำความสะอาดข้อมูล
      if (typeof cell === 'string') {
        cell = cell.trim();
        // ลบ quotes ที่ไม่จำเป็น
        if (cell.startsWith('"') && cell.endsWith('"')) {
          cell = cell.slice(1, -1);
        }
        // ลบ whitespace ที่ไม่จำเป็น
        cell = cell.replace(/^\s+|\s+$/g, '');
      }

      // ตรวจสอบและแปลงคะแนน (คอลัมน์ที่ 4 เป็นต้นไป)
      if (j >= 3) { // คอลัมน์คะแนน
        if (cell !== '' && isNaN(parseFloat(cell))) {
          errors.push(`แถว ${i + 1} คอลัมน์ ${j + 1}: คะแนนไม่ถูกต้อง`);
        }
        // แปลงเป็นตัวเลขถ้าเป็น string
        if (typeof cell === 'string' && cell !== '') {
          const numValue = parseFloat(cell);
          if (!isNaN(numValue)) {
            cell = numValue; // แปลงค่าใน cell แทนการใส่ใน cleanedRow[j]
          }
        }
      }

      cleanedRow.push(cell); // push cell ที่ทำความสะอาดแล้ว
    }

    // ตรวจสอบว่ามีข้อมูลในแถวหรือไม่
    if (cleanedRow.some(cell => cell !== '')) {
      cleanedData.push(cleanedRow);
    }
  }
  
  if (errors.length > 0) {
    return { error: errors.join(', ') };
  }
  
  return { data: cleanedData };
} 