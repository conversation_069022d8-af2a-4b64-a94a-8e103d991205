<!DOCTYPE html>
<html lang="th">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ScaryScore - RSU Dent</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style type="text/tailwindcss">
    /* ลบ .card และ .container-main ออก เพราะใช้ container/mx-auto/w-full/lg:max-w-[75%] ตามตัวอย่าง */
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <header class="border-b bg-white">
    <div class="container mx-auto px-4 py-4">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div class="flex justify-between items-center">
          <div class="flex flex-col">
            <h1 class="text-2xl font-bold text-gray-900">
              <i class="fa-solid fa-tooth mr-2"></i>Scary<span class="font-light">Score</span>
            </h1>
            <p class="text-sm text-gray-500 md:block hidden mt-1">ระบบประกาศคะแนนสอบ Dent RSU</p>
          </div>
        </div>
        <div id="user-info" class="text-sm text-gray-700 md:text-right"></div>
      </div>
    </div>
  </header>
  <main class="px-4 py-8">
    <div class="container mx-auto w-full lg:max-w-[75%] space-y-4">
      <div id="main-card" class="bg-white rounded-lg border shadow-sm p-4 mb-4">
        <div id="status" class="mb-4 text-center"></div>
        <div id="content"></div>
        <div id="student-courses"></div>
        <div id="course-scores"></div>
      </div>
    </div>
  </main>
  <footer class="text-center text-xs text-gray-400 py-2 border-t">&copy; 2025 <EMAIL></footer>
  <script>
    // เรียกตรวจสอบ auth
    google.script.run.withSuccessHandler(function(email) {
      if (!email) {
        document.getElementById('status').innerHTML = '<span class="text-red-500">กรุณาเข้าสู่ระบบด้วยบัญชี @rsu.ac.th</span>';
        document.getElementById('content').innerHTML = '';
        document.getElementById('user-info').innerHTML = '';
      } else {
        document.getElementById('status').innerHTML = '<span class="text-green-600">เข้าสู่ระบบสำเร็จ</span>';
        document.getElementById('user-info').innerHTML = '<i class="fa-solid fa-user"></i> ' + email;
        document.getElementById('content').innerHTML = '<div class="text-center">ยินดีต้อนรับสู่ระบบประกาศคะแนน</div>';
      }
    }).checkRsuAuth();
  </script>
  <script>
    function renderStudentCourses() {
      document.getElementById('student-courses').innerHTML = '<div class="text-gray-500">กำลังโหลดรายวิชา...</div>';
      google.script.run.withSuccessHandler(function(courses) {
        console.log('courses:', courses);
        if (!courses) {
          document.getElementById('student-courses').innerHTML = '<div class="text-red-500">เกิดข้อผิดพลาดในการโหลดข้อมูลรายวิชา (null)</div>';
          return;
        }
        if (!Array.isArray(courses)) {
          document.getElementById('student-courses').innerHTML = '<div class="text-red-500">ข้อมูลรายวิชาไม่ถูกต้อง: ' + JSON.stringify(courses) + '</div>';
          return;
        }
        if (courses.length === 0) {
          document.getElementById('student-courses').innerHTML = '<div class="text-gray-500">ไม่พบรายวิชาที่มีคะแนน</div>';
          return;
        }
        // debug: แสดง json ตรงๆ
        document.getElementById('student-courses').innerHTML = '<pre>' + JSON.stringify(courses, null, 2) + '</pre>';
        // render card แบบง่ายสุด
        let html = '<div class="grid gap-4 mt-4">';
        for (const c of courses) {
          html += `<div class="bg-white border rounded-lg shadow-sm p-4">` +
            `<div class="font-bold text-blue-700">${c.code}</div>` +
            `<div class="text-gray-700">${c.name}</div>` +
            `</div>`;
        }
        html += '</div>';
        document.getElementById('student-courses').innerHTML += html;
        document.getElementById('course-scores').innerHTML = '';
      })
      .withFailureHandler(function(error) {
        document.getElementById('student-courses').innerHTML = '<div class="text-red-500">เกิดข้อผิดพลาด: ' + error.message + '</div>';
        console.error('GAS error:', error);
      })
      .doGetStudentCourses();
    }

    function showCourseScores(sheetName, code, name, announceDate) {
      document.getElementById('course-scores').innerHTML = '<div class="text-gray-500">กำลังโหลดคะแนน...</div>';
      google.script.run.withSuccessHandler(function(result) {
        if (!result || !result.scores || result.scores.length === 0) {
          document.getElementById('course-scores').innerHTML = '<div class="text-gray-500">ไม่พบคะแนนในรายวิชานี้</div>';
          return;
        }
        let html = `<div class="mt-4 bg-gray-50 border rounded-lg p-4">` +
          `<div class="font-bold text-lg mb-2">${code} - ${name}</div>` +
          `<div class="text-xs text-gray-500 mb-2">ประกาศเมื่อ: ${announceDate}</div>` +
          `<table class="min-w-full text-sm mb-2"><thead><tr>` +
          '<th class="border px-2 py-1">Student ID</th>' +
          '<th class="border px-2 py-1">Name</th>' +
          '<th class="border px-2 py-1">Email</th>' +
          '<th class="border px-2 py-1">คะแนน</th>' +
          '<th class="border px-2 py-1">คะแนนเต็ม</th>' +
          '</tr></thead><tbody>';
        for (const s of result.scores) {
          html += `<tr><td class="border px-2 py-1">${s.studentId}</td><td class="border px-2 py-1">${s.studentName}</td><td class="border px-2 py-1">${s.studentEmail}</td><td class="border px-2 py-1">${s.score}</td><td class="border px-2 py-1">${s.fullScore}</td></tr>`;
        }
        html += '</tbody></table>';
        // Stat
        const st = result.stats;
        html += `<div class="mt-2 text-xs text-gray-700">` +
          `Mean: <span class="font-bold">${st.mean?.toFixed(2) ?? '-'}</span> | ` +
          `SD: <span class="font-bold">${st.sd?.toFixed(2) ?? '-'}</span> | ` +
          `Min: <span class="font-bold">${st.min ?? '-'}</span> | ` +
          `Max: <span class="font-bold">${st.max ?? '-'}</span> | ` +
          `Rank: <span class="font-bold">${st.rank ?? '-'}</span> | ` +
          `Percentile: <span class="font-bold">${st.percentile ?? '-'}</span>` +
          `</div></div>`;
        document.getElementById('course-scores').innerHTML = html;
        // log access (ใช้ email จาก user-info)
        const email = document.getElementById('user-info').textContent.trim();
        google.script.run.logAccess(email.split('@')[0], email, 'ดูคะแนน', code + ' - ' + name);
      }).doGetStudentCourseScores(sheetName);
    }

    // เรียกหลัง auth success
    function afterLogin() {
      renderStudentCourses();
    }

    document.addEventListener('DOMContentLoaded', function() {
      google.script.run.withSuccessHandler(function(email) {
        if (!email) {
          document.getElementById('status').innerHTML = '<span class="text-red-500">กรุณาเข้าสู่ระบบด้วยบัญชี @rsu.ac.th</span>';
          document.getElementById('content').innerHTML = '';
          document.getElementById('user-info').innerHTML = '';
        } else {
          document.getElementById('status').innerHTML = '<span class="text-green-600">เข้าสู่ระบบสำเร็จ</span>';
          document.getElementById('user-info').innerHTML = '<i class="fa-solid fa-user"></i> ' + email;
          document.getElementById('content').innerHTML = '<div class="text-center">ยินดีต้อนรับสู่ระบบประกาศคะแนน</div>';
          afterLogin();
        }
      }).checkRsuAuth();
    });
  </script>
  <style>
  </style>
</body>
</html> 