<!-- File: index.html -->
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบแจ้งคะแนนสอบนักเรียน</title>
    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@400;700&display=swap" rel="stylesheet">
    <!-- Styles -->
    <style>
        :root { --primary-color: #4a90e2; --success-color: #28a745; --danger-color: #dc3545; --warning-color: #ffc107; --light-bg: #f4f7f6; --white-bg: #ffffff; --dark-text: #333; --light-text: #f4f7f6; --grey-text: #6c757d; }
        body { font-family: 'Sarabun', sans-serif; background-color: var(--light-bg); color: var(--dark-text); margin: 0; padding: 20px; box-sizing: border-box; }
        .container { max-width: 1200px; margin: 0 auto; background-color: var(--white-bg); border-radius: 8px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); overflow: hidden; }
        header { background-color: var(--primary-color); color: white; padding: 20px; text-align: center; position: relative; }
        header h1 { margin: 0; font-size: 1.8em; }
        .header-btn { position: absolute; top: 50%; right: 20px; transform: translateY(-50%); background: #fff; color: var(--primary-color); border: 1px solid var(--primary-color); padding: 8px 15px; border-radius: 4px; cursor: pointer; font-weight: bold; }
        main { padding: 30px; }
        .form-section { text-align: center; margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background-color: #fafafa; }
        .form-section h2, .form-section h3 { margin-top: 0; color: var(--dark-text); }
        .form-section input { width: 100%; padding: 12px; border-radius: 4px; font-size: 1em; font-family: 'Sarabun', sans-serif; border: 1px solid #ccc; box-sizing: border-box; margin-bottom: 10px; }
        .form-section button { padding: 12px 30px; border-radius: 4px; font-size: 1em; font-weight: bold; cursor: pointer; border: none; color: white; margin: 10px 5px 0 5px; }
        #formSubmitButton { background-color: var(--success-color); }
        #formCancelButton { background-color: var(--grey-text); }
        .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 15px; text-align: left; align-items: end; }
        .score-input-group { display: grid; grid-template-columns: 1fr auto 1fr; gap: 8px; align-items: center; }
        .score-input-group span { text-align: center; font-size: 1.2em; color: var(--grey-text); }
        .score-block { border: 1px solid #e0e0e0; padding: 10px; border-radius: 6px; background: #fff; }
        .score-block label { font-weight: bold; margin-bottom: 8px; display: block; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 0.9em; table-layout: auto;}
        th, td { border: 1px solid #ddd; padding: 10px; text-align: center; vertical-align: middle; white-space: nowrap;}
        th { background-color: #f2f2f2; font-weight: bold; }
        .action-buttons button { margin: 2px; padding: 5px 8px; cursor: pointer; border-radius: 4px; border: none; color: white;}
        .edit-btn { background-color: var(--primary-color); }
        .delete-btn { background-color: var(--danger-color); }
        .decision-pass { color: var(--success-color); font-weight: bold; }
        .decision-fail { color: var(--danger-color); font-weight: bold; }
        #studentInfo { margin-bottom: 20px; background-color: #eaf2fa; padding: 15px; border-radius: 5px; font-size: 1.2em; text-align: center; }
        footer { background-color: var(--dark-text); color: var(--light-text); text-align: center; padding: 15px; font-size: 0.9em; }
        .import-export-section { margin-bottom: 30px; padding: 20px; border: 1px dashed var(--primary-color); border-radius: 8px; background-color: #eaf2fa; text-align: left; }
        .import-export-section h3 { margin-top: 0; }
        .import-export-section .btn { background-color: var(--primary-color); color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-weight: bold; margin-right: 10px; }
        .import-export-section input[type="file"] { border: 1px solid #ccc; display: inline-block; padding: 6px 12px; cursor: pointer; }
        [style*="display: none;"] { display: none !important; }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>ระบบแจ้งคะแนนสอบนักเรียน</h1>
            <button id="adminLoginBtn" class="header-btn">Admin Login</button>
            <button id="logoutBtn" class="header-btn" style="display: none;">Logout</button>
        </header>
        <main>
            <!-- Login Section -->
            <section id="loginSection" class="form-section" style="display: none;">
                <h2>Admin Login</h2>
                <form id="loginForm" style="max-width:400px; margin:auto;">
                    <input type="text" id="username" placeholder="Username" required>
                    <input type="password" id="password" placeholder="Password" required>
                    <button type="submit" style="background-color: var(--primary-color);">Login</button>
                </form>
            </section>
            
            <!-- Student Search Section -->
            <section id="studentSearchSection" class="form-section">
                <h2>ค้นหาผลการเรียน</h2>
                <form id="searchForm" style="max-width:500px; margin:auto;">
                    <label for="studentId">กรอกรหัสประจำตัวประชาชน 13 หลัก</label>
                    <input type="text" id="studentId" placeholder="x-xxxx-xxxxx-xx-x" required>
                    <button type="submit" style="background-color: var(--primary-color);">ค้นหา</button>
                </form>
            </section>
            
            <!-- Results Container -->
            <div id="resultsContainer" style="display: none;"></div>
            
            <!-- Admin Panel Section -->
            <section id="adminPanel" style="display: none;">
                <h2>จัดการข้อมูลนักเรียน</h2>
                
                <!-- Import/Export Section -->
                <div class="import-export-section">
                    <h3>นำเข้า / ส่งออก ข้อมูล</h3>
                    <p>คุณสามารถเพิ่ม/อัปเดตข้อมูลนักเรียนจำนวนมากได้อย่างรวดเร็วโดยใช้ไฟล์ CSV</p>
                    <ol>
                        <li><strong>ดาวน์โหลด Template:</strong> กดปุ่ม "Export Template" เพื่อรับไฟล์ CSV ที่มีหัวตารางถูกต้อง</li>
                        <li><strong>กรอกข้อมูล:</strong> เปิดไฟล์ด้วยโปรแกรม Spreadsheet และกรอกข้อมูลนักเรียน</li>
                        <li><strong>นำเข้าข้อมูล:</strong> เลือกไฟล์ที่กรอกข้อมูลแล้วและกดปุ่ม "Import"</li>
                    </ol>
                    <div>
                        <button id="exportTemplateBtn" class="btn">Export Template</button>
                        <input type="file" id="csvFile" accept=".csv">
                        <button id="importBtn" class="btn" style="background-color: var(--success-color);">Import</button>
                    </div>
                </div>
                
                <!-- Add/Edit Form Section -->
                <div class="form-section">
                    <h3 id="formTitle">เพิ่มข้อมูลรายวิชา (ทีละรายการ)</h3>
                    <form id="addEditForm">
                        <input type="hidden" name="is_editing" value="false">
                        <input type="hidden" name="original_id">
                        <input type="hidden" name="original_subject">
                        <div class="form-grid">
                            <input type="text" name="id" placeholder="รหัสประจำตัวประชาชน" required>
                            <input type="text" name="name" placeholder="ชื่อ-นามสกุล" required>
                            <input type="text" name="class" placeholder="ห้อง/ระดับชั้น" required>
                            <input type="text" name="subject" placeholder="รายวิชา" required>
                        </div>
                        <div class="form-grid" style="margin-top: 15px; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="score-block">
                                <label>คะแนนกลางภาค</label>
                                <div class="score-input-group">
                                    <input type="number" name="midterm_score" placeholder="ได้" required><span>/</span><input type="number" name="midterm_full" placeholder="เต็ม" required>
                                </div>
                                <input type="text" name="midterm_decision" placeholder="ผลการตัดสินกลางภาค" required style="margin-top:10px;">
                            </div>
                            <div class="score-block">
                                <label>คะแนนปลายภาค</label>
                                <div class="score-input-group">
                                    <input type="number" name="final_score" placeholder="ได้" required><span>/</span><input type="number" name="final_full" placeholder="เต็ม" required>
                                </div>
                                <input type="text" name="final_decision" placeholder="ผลการตัดสินปลายภาค" required style="margin-top:10px;">
                            </div>
                        </div>
                        <button type="submit" id="formSubmitButton">เพิ่มข้อมูล</button>
                        <button type="button" id="formCancelButton" style="display:none;">ยกเลิกแก้ไข</button>
                    </form>
                </div>
                
                <h3>ข้อมูลทั้งหมด</h3>
                <div id="adminTableContainer" style="overflow-x: auto;"></div>
            </section>
        </main>
        <footer>
            <p>© 2025 โรงเรียนดาสินธวานนนท์ | พัฒนาโดย นายคฑา จันทำมา</p>
        </footer>
    </div>

    <script>
        // Use an IIFE (Immediately Invoked Function Expression) to avoid polluting the global scope
        (function() {
            // ===================================
            //  THE FIX IS HERE
            // ===================================
            // This uses the 'url' variable passed from Code.gs
            const SCRIPT_URL = "<?= url ?>"; 

            // ===================================
            //  ELEMENT SELECTORS
            // ===================================
            const adminLoginBtn = document.getElementById('adminLoginBtn');
            const addEditForm = document.getElementById('addEditForm');
            const adminPanel = document.getElementById('adminPanel');
            const adminTableContainer = document.getElementById('adminTableContainer');
            const formCancelButton = document.getElementById('formCancelButton');
            const formSubmitButton = document.getElementById('formSubmitButton');
            const formTitle = document.getElementById('formTitle');
            const loginForm = document.getElementById('loginForm');
            const loginSection = document.getElementById('loginSection');
            const logoutBtn = document.getElementById('logoutBtn');
            const resultsContainer = document.getElementById('resultsContainer');
            const searchForm = document.getElementById('searchForm');
            const studentSearchSection = document.getElementById('studentSearchSection');
            const exportTemplateBtn = document.getElementById('exportTemplateBtn');
            const importBtn = document.getElementById('importBtn');
            const csvFile = document.getElementById('csvFile');

            // ===================================
            //  HELPER FUNCTIONS
            // ===================================

            /**
             * Sends a POST request to the server with a JSON payload.
             * Shows a loading indicator during the request.
             */
            async function postToServer(payload) {
                Swal.showLoading();
                try {
                    const response = await fetch(SCRIPT_URL, {
                        method: 'POST',
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error('Network response was not ok.');
                    const result = await response.json();
                    Swal.close();
                    return result;
                } catch (error) {
                    console.error("Error posting to server:", error);
                    Swal.fire('เกิดข้อผิดพลาด', 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้: ' + error.message, 'error');
                    return { success: false, message: error.message };
                }
            }

            /**
             * Renders the results for a single student.
             */
            function renderStudentResults(data) {
                const { id, name, 'class': className, scores } = data;
                let tableHtml = `
                    <div id="studentInfo">
                        <strong>รหัส:</strong> ${id}<br>
                        <strong>ชื่อ:</strong> ${name}<br>
                        <strong>ห้อง:</strong> ${className}
                    </div>
                    <div style="overflow-x: auto;">
                    <table>
                        <thead>
                            <tr>
                                <th>รายวิชา</th>
                                <th>คะแนนกลางภาค</th>
                                <th>ผลการตัดสิน</th>
                                <th>คะแนนปลายภาค</th>
                                <th>ผลการตัดสิน</th>
                            </tr>
                        </thead>
                        <tbody>`;
                
                scores.forEach(score => {
                    tableHtml += `
                        <tr>
                            <td>${score.subject}</td>
                            <td>${score.midterm_score} / ${score.midterm_full}</td>
                            <td class="${score.midterm_decision.includes('ผ่าน') ? 'decision-pass' : 'decision-fail'}">${score.midterm_decision}</td>
                            <td>${score.final_score} / ${score.final_full}</td>
                            <td class="${score.final_decision.includes('ผ่าน') ? 'decision-pass' : 'decision-fail'}">${score.final_decision}</td>
                        </tr>`;
                });

                tableHtml += `</tbody></table></div>`;
                resultsContainer.innerHTML = tableHtml;
                resultsContainer.style.display = 'block';
            }

            /**
             * Renders the main data table for the admin panel.
             */
            function renderAdminTable(data) {
                if (!data || data.length === 0) {
                    adminTableContainer.innerHTML = '<p>ยังไม่มีข้อมูลในระบบ</p>';
                    return;
                }
                
                let tableHtml = `
                    <table>
                        <thead>
                            <tr>
                                <th>รหัส</th>
                                <th>ชื่อ-สกุล</th>
                                <th>ห้อง</th>
                                <th>วิชา</th>
                                <th>กลางภาค</th>
                                <th>ปลายภาค</th>
                                <th>ดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>`;

                data.forEach(row => {
                    // Store the full row data in a data-* attribute for easy access
                    const rowDataJson = JSON.stringify(row);
                    tableHtml += `
                        <tr data-row='${rowDataJson.replace(/'/g, "'")}'>
                            <td>${row.id}</td>
                            <td>${row.name}</td>
                            <td>${row.class}</td>
                            <td>${row.subject}</td>
                            <td>${row.midterm_score}/${row.midterm_full} (${row.midterm_decision})</td>
                            <td>${row.final_score}/${row.final_full} (${row.final_decision})</td>
                            <td class="action-buttons">
                                <button class="edit-btn">แก้ไข</button>
                                <button class="delete-btn">ลบ</button>
                            </td>
                        </tr>`;
                });
                
                tableHtml += `</tbody></table>`;
                adminTableContainer.innerHTML = tableHtml;
            }

            /**
             * Loads all data for the admin view.
             */
            async function loadAllAdminData() {
                const result = await postToServer({ action: 'getAll' });
                if (result.success) {
                    renderAdminTable(result.data);
                } else {
                    Swal.fire('ผิดพลาด', 'ไม่สามารถโหลดข้อมูลได้', 'error');
                }
            }

            /**
             * Updates the UI to show the admin panel.
             */
            const updateUIForLogin = () => {
                loginSection.style.display = 'none';
                studentSearchSection.style.display = 'none';
                resultsContainer.innerHTML = '';
                resultsContainer.style.display = 'none';
                adminPanel.style.display = 'block';
                adminLoginBtn.style.display = 'none';
                logoutBtn.style.display = 'block';
                loadAllAdminData();
            };

            /**
             * Updates the UI to show the student search/login view.
             */
            const updateUIForLogout = () => {
                adminPanel.style.display = 'none';
                loginSection.style.display = 'none';
                studentSearchSection.style.display = 'block';
                adminLoginBtn.style.display = 'block';
                logoutBtn.style.display = 'none';
                resultsContainer.innerHTML = '';
                resultsContainer.style.display = 'none';
                loginForm.reset();
            };

            /**
             * Resets the add/edit form to its initial state.
             */
            const resetAddEditForm = () => {
                addEditForm.reset();
                addEditForm.is_editing.value = 'false';
                formTitle.textContent = "เพิ่มข้อมูลรายวิชา (ทีละรายการ)";
                formSubmitButton.textContent = "เพิ่มข้อมูล";
                formCancelButton.style.display = "none";
            };

            // ===================================
            //  EVENT LISTENERS
            // ===================================

            adminLoginBtn.addEventListener('click', () => {
                studentSearchSection.style.display = 'none';
                resultsContainer.style.display = 'none';
                loginSection.style.display = 'block';
            });

            logoutBtn.addEventListener('click', updateUIForLogout);

            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                // Simple hardcoded credentials. For a real app, use a more secure method.
                if (document.getElementById('username').value === 'admin' && document.getElementById('password').value === 'admin12345') {
                    updateUIForLogin();
                } else {
                    Swal.fire('ผิดพลาด', 'Username หรือ Password ไม่ถูกต้อง', 'error');
                }
            });

            searchForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const studentId = document.getElementById('studentId').value.trim();
                if (!studentId) return;
                resultsContainer.innerHTML = '';
                resultsContainer.style.display = 'none';
                const result = await postToServer({ action: 'getStudent', id: studentId });
                if (result.success && result.scores && result.scores.length > 0) {
                    renderStudentResults(result);
                } else {
                    Swal.fire('ไม่พบข้อมูล', result.message || 'ไม่พบข้อมูลนักเรียน กรุณาตรวจสอบรหัสอีกครั้ง', 'warning');
                }
            });

            addEditForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const dataObject = {};
                formData.forEach((value, key) => dataObject[key] = value);
                
                const payload = {
                    action: formData.get('is_editing') === 'true' ? 'edit' : 'add',
                    data: dataObject
                };

                const result = await postToServer(payload);
                if (result.success) {
                    Swal.fire('สำเร็จ!', result.message, 'success');
                    resetAddEditForm();
                    loadAllAdminData();
                } else {
                    Swal.fire('ผิดพลาด', result.message, 'error');
                }
            });

            adminTableContainer.addEventListener('click', async (e) => {
                const target = e.target;
                const row = target.closest('tr');
                if (!row) return;

                const rowData = JSON.parse(row.dataset.row.replace(/'/g, "'"));

                if (target.classList.contains('delete-btn')) {
                    Swal.fire({
                        title: 'ยืนยันการลบ?',
                        text: `คุณต้องการลบข้อมูลวิชา "${rowData.subject}" ของรหัส "${rowData.id}" ใช่หรือไม่?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'ใช่, ลบเลย!',
                        cancelButtonText: 'ยกเลิก'
                    }).then(async (result) => {
                        if (result.isConfirmed) {
                            const deleteResult = await postToServer({ action: 'delete', id: rowData.id, subject: rowData.subject });
                            if (deleteResult.success) {
                                Swal.fire('ลบแล้ว!', deleteResult.message, 'success');
                                loadAllAdminData();
                            } else {
                                Swal.fire('ผิดพลาด', deleteResult.message, 'error');
                            }
                        }
                    });
                } else if (target.classList.contains('edit-btn')) {
                    addEditForm.is_editing.value = 'true';
                    addEditForm.original_id.value = rowData.id;
                    addEditForm.original_subject.value = rowData.subject;
                    // Populate form fields
                    for (const key in rowData) {
                        if (addEditForm.elements[key]) {
                            addEditForm.elements[key].value = rowData[key];
                        }
                    }
                    formTitle.textContent = "แก้ไขข้อมูลรายวิชา";
                    formSubmitButton.textContent = "บันทึกการแก้ไข";
                    formCancelButton.style.display = "inline-block";
                    window.scrollTo({ top: addEditForm.offsetTop, behavior: 'smooth' });
                }
            });

            formCancelButton.addEventListener('click', resetAddEditForm);
            
            exportTemplateBtn.addEventListener('click', () => {
                const headers = ["รหัสประจำตัวประชาชน", "ชื่อ-นามสกุล", "ห้อง/ระดับชั้น", "รายวิชา", "คะแนนกลางภาค", "คะแนนเต็มกลางภาค", "ผลการตัดสินกลางภาค", "คะแนนปลายภาค", "คะแนนเต็มปลายภาค", "ผลการตัดสินปลายภาค"];
                // Use a Byte Order Mark (BOM) for Excel compatibility with Thai characters
                const bom = "\uFEFF";
                let csvContent = bom + headers.join(",") + "\n";
                const encodedUri = encodeURI("data:text/csv;charset=utf-8," + csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", "student_data_template.csv");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });

            importBtn.addEventListener('click', () => {
                if (!csvFile.files || csvFile.files.length === 0) {
                    Swal.fire('ผิดพลาด', 'กรุณาเลือกไฟล์ CSV ที่จะนำเข้า', 'warning');
                    return;
                }
                Papa.parse(csvFile.files[0], {
                    header: true,
                    skipEmptyLines: true,
                    encoding: "UTF-8", // Specify encoding for Thai characters
                    complete: async function(results) {
                        if (results.errors.length > 0) {
                            console.error("CSV Parsing Errors:", results.errors);
                            Swal.fire('เกิดข้อผิดพลาด', 'ไม่สามารถอ่านไฟล์ CSV ได้อย่างสมบูรณ์ กรุณาตรวจสอบรูปแบบไฟล์', 'error');
                            return;
                        }

                        const dataToImport = results.data.map(row => ({
                            id: row["รหัสประจำตัวประชาชน"],
                            name: row["ชื่อ-นามสกุล"],
                            class: row["ห้อง/ระดับชั้น"],
                            subject: row["รายวิชา"],
                            midterm_score: row["คะแนนกลางภาค"],
                            midterm_full: row["คะแนนเต็มกลางภาค"],
                            midterm_decision: row["ผลการตัดสินกลางภาค"],
                            final_score: row["คะแนนปลายภาค"],
                            final_full: row["คะแนนเต็มปลายภาค"],
                            final_decision: row["ผลการตัดสินปลายภาค"],
                        }));
                        
                        const result = await postToServer({ action: 'importData', data: dataToImport });
                        if(result.success) {
                             Swal.fire('สำเร็จ!', result.message, 'success');
                             csvFile.value = ""; // Clear file input
                             loadAllAdminData();
                        } else {
                             Swal.fire('นำเข้าข้อมูลล้มเหลว', result.message, 'error');
                        }
                    },
                    error: function(err) {
                        Swal.fire('เกิดข้อผิดพลาด', 'ไม่สามารถอ่านไฟล์ CSV ได้: ' + err.message, 'error');
                    }
                });
            });

        })();
    </script>
</body>
</html>