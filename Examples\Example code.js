//20250505 แยกจาก spreadsheet หลัก มาเป็น app script project
function getInitialsFromEmail(email) {
  // ตัดส่วน @rsu.ac.th ออก
  const username = email.split('@')[0];
  
  // กรณีที่ 1: มีจุด (.) ในชื่อ
  if (username.includes('.')) {
    const parts = username.split('.');
    return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
  }
  
  // กรณีที่ 2: ไม่มีจุด แต่มีสระ
  const vowels = ['a', 'e', 'i', 'o', 'u'];
  for (let i = 0; i < username.length - 1; i++) {
    if (vowels.includes(username[i].toLowerCase())) {
      return (username[0] + username[i + 1]).toUpperCase();
    }
  }
  
  // กรณีที่ 3: ใช้สองตัวแรก
  return username.substring(0, 2).toUpperCase();
}

function doGet() {
  // บันทึกการเข้าใช้งาน
  accessLog('เข้าใช้งานระบบ', null);
  
  // Get current user email
  const userEmail = Session.getActiveUser().getEmail();
  const username = userEmail.split('@')[0];
  const initials = getInitialsFromEmail(userEmail);
  
  // Create template and pass user data
  const template = HtmlService.createTemplateFromFile('Index');
  template.username = username;
  template.initials = initials;
  
  return template
    .evaluate()
    .setTitle('DailyRemover')
    .setFaviconUrl("https://cdn-icons-png.flaticon.com/512/11003/11003975.png")
    .addMetaTag('viewport', 'width=device-width, initial-scale=1');
}

function getScoreData() {
  try {
    // บันทึกการเรียกดูข้อมูล
    accessLog('เรียกดูข้อมูล', null);
    
    // ใช้ SpreadsheetApp.openById แทน getActiveSpreadsheet
    const MAIN_SPREADSHEET_ID = "148mOqTFdHSDQg30sorlKRHKjw49GWdlLfItEns3-ddE";
    const ss = SpreadsheetApp.openById(MAIN_SPREADSHEET_ID);
    Logger.log('Spreadsheet URL: ' + ss.getUrl());
    
    // เปลี่ยนจาก Sheet1 เป็น Sheet2
    const sheet = ss.getSheetByName('Sheet2');
    Logger.log('Sheet name: ' + sheet.getName());
    
    const dataRange = sheet.getDataRange();
    const data = dataRange.getValues();
    Logger.log('Total rows in sheet: ' + data.length);
    
    // Get headers and data rows
    const headers = data[0];
    const rows = data.slice(1);
    Logger.log('Headers: ' + JSON.stringify(headers));
    
    // Get current user email
    const userEmail = Session.getActiveUser().getEmail();
    Logger.log('Current user email: ' + userEmail);
    
    // คาบที่มีคลินิก
    const CLINIC_SCHEDULE = {
      "Monday": ["Morning", "Afternoon"],
      "Tuesday": ["Afternoon", "Evening"],
      "Wednesday": ["Afternoon"],
      "Thursday": ["Afternoon", "Evening"],
      "Friday": ["Morning", "Afternoon"]
    };
    
    // ดึงข้อมูลคาบคลินิกเพิ่มเติมจาก sheet additionalClinic
    let additionalClinicMap = new Map();
    try {
      // เข้าถึง spreadsheet ที่มีข้อมูลคาบคลินิกเพิ่มเติม
      const additionalClinicSS = SpreadsheetApp.openById('1235X1TbwQLW3GvD-3o1uiBKeUwV0syoWEOGjni_Gy_k');
      const additionalClinicSheet = additionalClinicSS.getSheetByName('additionalClinic');
      
      // ถ้าพบ sheet ให้ดึงข้อมูล
      if (additionalClinicSheet) {
        const additionalClinicData = additionalClinicSheet.getDataRange().getValues();
        // ข้ามแถวแรก (หัวตาราง)
        const additionalClinicRows = additionalClinicData.slice(1);
        
        // แปลงข้อมูลให้อยู่ในรูปแบบของ Map โดยใช้ key เป็น "วันที่_คาบเรียน"
        additionalClinicRows.forEach(row => {
          if (row[0] && row[1]) { // ตรวจสอบว่ามีวันที่และคาบเรียน
            let dateStr = '';
            if (row[0] instanceof Date) {
              // แปลงวันที่เป็นรูปแบบ MM/DD/YYYY
              const date = row[0];
              const month = date.getMonth() + 1;
              const day = date.getDate();
              const year = date.getFullYear();
              dateStr = `${month}/${day}/${year}`;
            } else if (typeof row[0] === 'string') {
              // ใช้ string วันที่ตามที่มีในข้อมูล
              dateStr = row[0];
            }
            
            const session = row[1];
            const key = `${dateStr}_${session}`;
            const description = row[2] || ''; // คำอธิบายเพิ่มเติม (ถ้ามี)
            
            additionalClinicMap.set(key, {
              date: dateStr,
              session: session,
              description: description
            });
            
            Logger.log(`Found additional clinic: ${key} - ${description}`);
          }
        });
        
        Logger.log(`Loaded ${additionalClinicMap.size} additional clinic sessions`);
      } else {
        Logger.log('additionalClinic sheet not found');
      }
    } catch (e) {
      Logger.log('Error loading additional clinic data: ' + e.toString());
      // ไม่ throw error เพื่อให้โปรแกรมทำงานต่อไปได้แม้จะไม่สามารถโหลดข้อมูลคาบคลินิกเพิ่มเติมได้
    }
    
    // ฟังก์ชันตรวจสอบว่าคาบนี้มีคลินิกหรือไม่
    function isClinicSession(date, session) {
      try {
        // ถ้าเป็น Red or Yellow card only ให้ถือว่าเป็นคาบที่มีคลินิก (ไม่แสดงไอคอน error)
        if (session === "Red or Yellow card only") {
          return true;
        }
        
        // ตรวจสอบตารางคลินิกปกติ
        const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'long' });
        if (CLINIC_SCHEDULE[dayOfWeek] && CLINIC_SCHEDULE[dayOfWeek].includes(session)) {
          return true;
        }
        
        // ตรวจสอบคาบคลินิกเพิ่มเติม
        if (additionalClinicMap.size > 0) {
          // แปลงวันที่เป็นรูปแบบ MM/DD/YYYY เพื่อเปรียบเทียบกับข้อมูลในชีท additionalClinic
          const dateObj = new Date(date);
          const month = dateObj.getMonth() + 1;
          const day = dateObj.getDate();
          const year = dateObj.getFullYear();
          const formattedDate = `${month}/${day}/${year}`;
          
          // สร้าง key สำหรับค้นหาในตาราง additionalClinicMap
          const key = `${formattedDate}_${session}`;
          
          // ถ้าพบข้อมูลในตาราง additionalClinicMap ให้ถือว่าเป็นคาบคลินิก
          if (additionalClinicMap.has(key)) {
            Logger.log(`Found clinic in additional schedule: ${key}`);
            return true;
          }
        }
        
        // ถ้าไม่ใช่ทั้งคาบคลินิกปกติและคาบคลินิกเพิ่มเติม ให้ถือว่าไม่มีคลินิก
        return false;
      } catch (e) {
        Logger.log('Error checking clinic session: ' + e.toString());
        return true; // ถ้ามีข้อผิดพลาด ให้ถือว่าเป็นคาบที่มีคลินิก (ไม่แสดงไอคอน)
      }
    }
    
    // Filter rows by user email (Column B - index 1) AND non-empty studentId (Column F - index 5)
    const filteredRows = rows.filter(row => {
      const hasEmail = row[1] === userEmail;
      const hasStudentId = row[5] !== null && row[5] !== undefined && row[5].toString().trim() !== '';
      return hasEmail && hasStudentId;
    });
    
    Logger.log('Filtered rows count: ' + filteredRows.length);
    if (filteredRows.length > 0) {
      Logger.log('First filtered row: ' + JSON.stringify(filteredRows[0]));
    }

    // สร้าง Map เพื่อตรวจสอบการซ้ำกับอาจารย์ท่านอื่น
    const duplicateTeacherMap = new Map();
    rows.forEach((row, index) => {
      // ข้ามการตรวจสอบถ้า Session หรือ Work เป็น Red or Yellow card only
      if (row[3] === "Red or Yellow card only" || row[6] === "Red or Yellow card only") return;
      
      if (row[5] && row[5].toString().trim() !== '') { // มี studentId
        const key = `${row[2]}_${row[3]}_${row[5]}`; // workingDate_session_studentId
        if (!duplicateTeacherMap.has(key)) {
          duplicateTeacherMap.set(key, [{
            index: index,
            row: row,
            email: row[1]
          }]);
        } else {
          duplicateTeacherMap.get(key).push({
            index: index,
            row: row,
            email: row[1]
          });
        }
      }
    });
    
    // หา rows ที่ซ้ำกับของเรา
    const duplicateRows = [];
    filteredRows.forEach(row => {
      const key = `${row[2]}_${row[3]}_${row[5]}`;
      const duplicates = duplicateTeacherMap.get(key) || [];
      const otherTeacherRows = duplicates
        .filter(entry => entry.email !== userEmail)
        .map(entry => entry.row);
      duplicateRows.push(...otherTeacherRows);
    });

    // รวม rows ของเราและ rows ที่ซ้ำ
    const allRelevantRows = [...filteredRows, ...duplicateRows];
    
    // คำนวณสถิติ Same-day Assessment
    let sameDayCount = 0;
    let monthlyData = {};
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Transform data to match our UI structure
    const transformedData = allRelevantRows.map((row, index) => {
      // Convert date strings to ISO format
      const timestamp = new Date(row[0]);
      
      // แก้ไขการแปลงวันที่ในคอลัมน์ C โดยเพิ่มเวลา 12:00:00 เพื่อป้องกันปัญหาเรื่อง timezone
      let workingDate;
      if (row[2] instanceof Date) {
        workingDate = new Date(row[2]);
        workingDate.setHours(12, 0, 0, 0);
      } else if (typeof row[2] === 'string') {
        const dateParts = row[2].split('/');
        if (dateParts.length === 3) {
          const month = parseInt(dateParts[0]) - 1;
          const day = parseInt(dateParts[1]);
          const year = parseInt(dateParts[2]);
          workingDate = new Date(year, month, day, 12, 0, 0);
        } else {
          workingDate = new Date(row[2]);
          workingDate.setHours(12, 0, 0, 0);
        }
      } else {
        workingDate = new Date(row[2]);
        workingDate.setHours(12, 0, 0, 0);
      }

      // ตรวจสอบการซ้ำกับอาจารย์ท่านอื่น
      const key = `${row[2]}_${row[3]}_${row[5]}`;
      const duplicateEntries = duplicateTeacherMap.get(key) || [];
      const duplicateTeachers = duplicateEntries
        .filter(entry => entry.email !== row[1]) // กรองอาจารย์ที่ไม่ใช่เจ้าของ row นี้
        .map(entry => ({
          email: entry.email.split('@')[0],
          work: entry.row[6]
        }));
      
      // ตรวจสอบว่าเป็นข้อมูลซ้ำหรือไม่
      const isDuplicate = duplicateEntries.length > 1;
      const isOurCard = row[1] === userEmail;
      const hasDuplicateWithOtherTeacher = duplicateTeachers.length > 0;
      
      // ตรวจสอบว่าเป็นคาบที่ไม่มีคลินิกหรือไม่
      const hasNoClinic = !isClinicSession(workingDate, row[3]);
      
      // เพิ่มข้อมูลคำอธิบายคาบคลินิกเพิ่มเติม (ถ้ามี)
      let hasAdditionalClinicInfo = null;
      if (!hasNoClinic && row[3] !== "Red or Yellow card only") {
        // ตรวจสอบว่าเป็นคาบในตารางปกติหรือไม่
        const dayOfWeek = new Date(workingDate).toLocaleDateString('en-US', { weekday: 'long' });
        const isRegularClinic = CLINIC_SCHEDULE[dayOfWeek] && CLINIC_SCHEDULE[dayOfWeek].includes(row[3]);
        
        // ถ้าไม่ใช่คาบปกติ แต่ยังเป็นคาบที่มีคลินิก แสดงว่าเป็นคาบคลินิกเพิ่มเติม
        if (!isRegularClinic) {
          // แปลงวันที่เพื่อค้นหาในตาราง additionalClinicMap
          const dateObj = new Date(workingDate);
          const month = dateObj.getMonth() + 1;
          const day = dateObj.getDate();
          const year = dateObj.getFullYear();
          const formattedDate = `${month}/${day}/${year}`;
          
          const key = `${formattedDate}_${row[3]}`;
          if (additionalClinicMap.has(key)) {
            hasAdditionalClinicInfo = additionalClinicMap.get(key).description || 'คาบคลินิกเพิ่มเติม';
          }
        }
      }
      
      // ตรวจสอบว่าเป็นการเดลี่ก่อนวันปฏิบัติงานหรือไม่
      const submissionDate = new Date(row[0]);
      submissionDate.setHours(0, 0, 0, 0);
      const workDate = new Date(workingDate);
      workDate.setHours(0, 0, 0, 0);
      const isSubmitBeforeWork = workDate > submissionDate;
      
      // ตรวจสอบ Same-day Assessment
      // เปรียบเทียบเฉพาะวัน เดือน ปี โดยแปลงเป็น string ในรูปแบบ YYYY-MM-DD
      const submissionDateStr = submissionDate.toISOString().split('T')[0];
      const workDateStr = workDate.toISOString().split('T')[0];
      const isSameDay = submissionDateStr === workDateStr;
      
      if (isSameDay) {
        sameDayCount++;
      }
      
      // เก็บข้อมูลรายเดือน
      const rowMonth = submissionDate.getMonth();
      const rowYear = submissionDate.getFullYear();
      const monthKey = `${rowYear}-${rowMonth}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          count: 0,
          days: new Set()
        };
      }
      
      monthlyData[monthKey].count++;
      monthlyData[monthKey].days.add(submissionDate.toISOString().split('T')[0]);
      
      return {
        id: index + 1,
        timestamp: timestamp.toISOString(),
        email: row[1],
        workingDate: workingDate.toISOString(),
        session: row[3],
        studentName: row[4],
        studentId: row[5],
        work: row[6],
        isDuplicate: isDuplicate,
        hasNoClinic: hasNoClinic,
        isSubmitBeforeWork: isSubmitBeforeWork,
        hasDuplicateWithOtherTeacher: hasDuplicateWithOtherTeacher,
        duplicateTeachers: duplicateTeachers,
        isOurCard: isOurCard,
        hasAdditionalClinicInfo: hasAdditionalClinicInfo,
        // ไม่รวมข้อมูล scoreSum และ redyellowSum ในการโหลดข้อมูลครั้งแรก
        rowIndex: index // เก็บ index ของแถวไว้สำหรับการโหลดข้อมูลเพิ่มเติม
      };
    });
    
    // คำนวณ % Same-day Assessment
    const sameDayPercentage = allRelevantRows.length > 0 
      ? Math.round((sameDayCount / allRelevantRows.length) * 100) 
      : 0;
    
    Logger.log('Same-day count: ' + sameDayCount + ' out of ' + allRelevantRows.length + ' = ' + sameDayPercentage + '%');
    
    // คำนวณ Entry Accuracy
    let duplicateErrorCount = 0;
    let noClinicErrorCount = 0;
    let submitBeforeWorkErrorCount = 0;
    
    // สร้าง Map เพื่อนับจำนวนรายการซ้ำในแต่ละกลุ่ม
    const duplicateErrorMap = new Map();
    
    // นับข้อผิดพลาดแต่ละประเภท
    transformedData.forEach(score => {
      // ข้ามการตรวจสอบถ้าเป็น Red or Yellow card only
      if (score.session === "Red or Yellow card only") return;
      
      // นับข้อผิดพลาดจากคาบที่ไม่มีคลินิก
      if (score.hasNoClinic) {
        noClinicErrorCount++;
      }
      
      // นับข้อผิดพลาดจากการให้คะแนนล่วงหน้า
      if (score.isSubmitBeforeWork) {
        submitBeforeWorkErrorCount++;
      }
      
      // เก็บข้อมูลรายการซ้ำเพื่อคำนวณภายหลัง
      if (score.isDuplicate) {
        const key = `${score.workingDate}_${score.session}_${score.studentId}`;
        if (!duplicateErrorMap.has(key)) {
          duplicateErrorMap.set(key, 1);
        } else {
          duplicateErrorMap.set(key, duplicateErrorMap.get(key) + 1);
        }
      }
    });
    
    // คำนวณข้อผิดพลาดจากการให้คะแนนซ้ำ (จำนวนรายการในแต่ละกลุ่ม - 1)
    duplicateErrorMap.forEach((count, key) => {
      duplicateErrorCount += (count - 1);
    });
    
    // รวมข้อผิดพลาดทั้งหมด
    const totalErrorCount = duplicateErrorCount + noClinicErrorCount + submitBeforeWorkErrorCount;
    
    // คำนวณ % ความถูกต้อง
    const entryAccuracy = allRelevantRows.length > 0
      ? Math.round(((allRelevantRows.length - totalErrorCount) / allRelevantRows.length) * 1000) / 10
      : 0;
    
    Logger.log('Transformed data count: ' + transformedData.length);
    if (transformedData.length > 0) {
      Logger.log('First transformed item: ' + JSON.stringify(transformedData[0]));
    }
    
    // ส่งข้อมูลพร้อมสถิติกลับไป
    return {
      scores: transformedData,
      stats: {
        sameDayPercentage: sameDayPercentage,
        entryAccuracy: entryAccuracy
      }
    };
    
  } catch (error) {
    Logger.log('Error in getScoreData: ' + error.toString());
    Logger.log('Stack trace: ' + error.stack);
    throw error;
  }
}

/**
 * ฟังก์ชันสำหรับโหลดข้อมูลเพิ่มเติม (scoreSum และ redyellowSum)
 * @return {Object} ข้อมูลเพิ่มเติมสำหรับแต่ละ score
 */
function getAdditionalScoreData() {
  try {
    // บันทึกการเรียกดูข้อมูลเพิ่มเติม
    accessLog('เรียกดูข้อมูลเพิ่มเติม', null);
    
    // ใช้ SpreadsheetApp.openById แทน getActiveSpreadsheet
    const MAIN_SPREADSHEET_ID = "148mOqTFdHSDQg30sorlKRHKjw49GWdlLfItEns3-ddE";
    const ss = SpreadsheetApp.openById(MAIN_SPREADSHEET_ID);
    
    // เปลี่ยนจาก Sheet1 เป็น Sheet2
    const sheet = ss.getSheetByName('Sheet2');
    const dataRange = sheet.getDataRange();
    const data = dataRange.getValues();
    
    // Get headers and data rows
    const headers = data[0];
    const rows = data.slice(1);
    
    // Get current user email
    const userEmail = Session.getActiveUser().getEmail();
    
    // คาบที่มีคลินิก
    const CLINIC_SCHEDULE = {
      "Monday": ["Morning", "Afternoon"],
      "Tuesday": ["Afternoon", "Evening"],
      "Wednesday": ["Afternoon"],
      "Thursday": ["Afternoon", "Evening"],
      "Friday": ["Morning", "Afternoon"]
    };
    
    // ดึงข้อมูลคาบคลินิกเพิ่มเติมจาก sheet additionalClinic
    let additionalClinicMap = new Map();
    try {
      // เข้าถึง spreadsheet ที่มีข้อมูลคาบคลินิกเพิ่มเติม
      const additionalClinicSS = SpreadsheetApp.openById('1235X1TbwQLW3GvD-3o1uiBKeUwV0syoWEOGjni_Gy_k');
      const additionalClinicSheet = additionalClinicSS.getSheetByName('additionalClinic');
      
      // ถ้าพบ sheet ให้ดึงข้อมูล
      if (additionalClinicSheet) {
        const additionalClinicData = additionalClinicSheet.getDataRange().getValues();
        // ข้ามแถวแรก (หัวตาราง)
        const additionalClinicRows = additionalClinicData.slice(1);
        
        // แปลงข้อมูลให้อยู่ในรูปแบบของ Map โดยใช้ key เป็น "วันที่_คาบเรียน"
        additionalClinicRows.forEach(row => {
          if (row[0] && row[1]) { // ตรวจสอบว่ามีวันที่และคาบเรียน
            let dateStr = '';
            if (row[0] instanceof Date) {
              // แปลงวันที่เป็นรูปแบบ MM/DD/YYYY
              const date = row[0];
              const month = date.getMonth() + 1;
              const day = date.getDate();
              const year = date.getFullYear();
              dateStr = `${month}/${day}/${year}`;
            } else if (typeof row[0] === 'string') {
              // ใช้ string วันที่ตามที่มีในข้อมูล
              dateStr = row[0];
            }
            
            const session = row[1];
            const key = `${dateStr}_${session}`;
            const description = row[2] || ''; // คำอธิบายเพิ่มเติม (ถ้ามี)
            
            additionalClinicMap.set(key, {
              date: dateStr,
              session: session,
              description: description
            });
            
            Logger.log(`Found additional clinic: ${key} - ${description}`);
          }
        });
        
        Logger.log(`Loaded ${additionalClinicMap.size} additional clinic sessions`);
      } else {
        Logger.log('additionalClinic sheet not found');
      }
    } catch (e) {
      Logger.log('Error loading additional clinic data: ' + e.toString());
      // ไม่ throw error เพื่อให้โปรแกรมทำงานต่อไปได้แม้จะไม่สามารถโหลดข้อมูลคาบคลินิกเพิ่มเติมได้
    }
    
    // ฟังก์ชันตรวจสอบว่าคาบนี้มีคลินิกหรือไม่
    function isClinicSession(date, session) {
      try {
        // ถ้าเป็น Red or Yellow card only ให้ถือว่าเป็นคาบที่มีคลินิก (ไม่แสดงไอคอน error)
        if (session === "Red or Yellow card only") {
          return true;
        }
        
        // ตรวจสอบตารางคลินิกปกติ
        const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'long' });
        if (CLINIC_SCHEDULE[dayOfWeek] && CLINIC_SCHEDULE[dayOfWeek].includes(session)) {
          return true;
        }
        
        // ตรวจสอบคาบคลินิกเพิ่มเติม
        if (additionalClinicMap.size > 0) {
          // แปลงวันที่เป็นรูปแบบ MM/DD/YYYY เพื่อเปรียบเทียบกับข้อมูลในชีท additionalClinic
          const dateObj = new Date(date);
          const month = dateObj.getMonth() + 1;
          const day = dateObj.getDate();
          const year = dateObj.getFullYear();
          const formattedDate = `${month}/${day}/${year}`;
          
          // สร้าง key สำหรับค้นหาในตาราง additionalClinicMap
          const key = `${formattedDate}_${session}`;
          
          // ถ้าพบข้อมูลในตาราง additionalClinicMap ให้ถือว่าเป็นคาบคลินิก
          if (additionalClinicMap.has(key)) {
            Logger.log(`Found clinic in additional schedule: ${key}`);
            return true;
          }
        }
        
        // ถ้าไม่ใช่ทั้งคาบคลินิกปกติและคาบคลินิกเพิ่มเติม ให้ถือว่าไม่มีคลินิก
        return false;
      } catch (e) {
        Logger.log('Error checking clinic session: ' + e.toString());
        return true; // ถ้ามีข้อผิดพลาด ให้ถือว่าเป็นคาบที่มีคลินิก (ไม่แสดงไอคอน)
      }
    }
    
    // Filter rows by user email (Column B - index 1) AND non-empty studentId (Column F - index 5)
    const filteredRows = rows.filter(row => {
      const hasEmail = row[1] === userEmail;
      const hasStudentId = row[5] !== null && row[5] !== undefined && row[5].toString().trim() !== '';
      return hasEmail && hasStudentId;
    });
    
    Logger.log('Filtered rows for additional data: ' + filteredRows.length);
    
    // สร้าง Map เพื่อตรวจสอบการซ้ำกับอาจารย์ท่านอื่น
    const duplicateTeacherMap = new Map();
    rows.forEach((row, index) => {
      // ข้ามการตรวจสอบถ้า Session หรือ Work เป็น Red or Yellow card only
      if (row[3] === "Red or Yellow card only" || row[6] === "Red or Yellow card only") return;
      
      if (row[5] && row[5].toString().trim() !== '') { // มี studentId
        const key = `${row[2]}_${row[3]}_${row[5]}`; // workingDate_session_studentId
        if (!duplicateTeacherMap.has(key)) {
          duplicateTeacherMap.set(key, [{
            index: index,
            row: row,
            email: row[1]
          }]);
        } else {
          duplicateTeacherMap.get(key).push({
            index: index,
            row: row,
            email: row[1]
          });
        }
      }
    });
    
    // หา rows ที่ซ้ำกับของเรา
    const duplicateRows = [];
    filteredRows.forEach(row => {
      const key = `${row[2]}_${row[3]}_${row[5]}`;
      const duplicates = duplicateTeacherMap.get(key) || [];
      const otherTeacherRows = duplicates
        .filter(entry => entry.email !== userEmail)
        .map(entry => entry.row);
      duplicateRows.push(...otherTeacherRows);
    });

    // รวม rows ของเราและ rows ที่ซ้ำ
    const allRelevantRows = [...filteredRows, ...duplicateRows];
    
    Logger.log('All relevant rows for additional data: ' + allRelevantRows.length);
    
    // ฟังก์ชันสำหรับนับจำนวน E, S, P, U จากคอลัมน์ที่กำหนด
    function countScoreTypes(row, columnRanges) {
      let counts = { E: 0, S: 0, P: 0, U: 0 };
      
      // วนลูปผ่านช่วงคอลัมน์ที่กำหนด
      columnRanges.forEach(range => {
        const [start, end] = range;
        for (let i = start; i <= end; i++) {
          const value = row[i];
          if (!value) continue;
          
          const valueStr = value.toString().trim().toUpperCase();
          
          if (valueStr === 'E') {
            counts.E++;
          } else if (valueStr === 'S' || valueStr === 'SATISFY' || valueStr === 'SATISFIED') {
            counts.S++;
          } else if (valueStr === 'P') {
            counts.P++;
          } else if (valueStr === 'U' || valueStr === 'UNSATISFY' || valueStr === 'UNSATISFIED') {
            counts.U++;
          }
        }
      });
      
      return counts;
    }
    
    // ฟังก์ชันสำหรับสร้าง scoreSum string
    function formatScoreSum(counts) {
      let result = '';
      if (counts.E > 0) result += `${counts.E}E`;
      if (counts.S > 0) result += `${counts.S}S`;
      if (counts.P > 0) result += `${counts.P}P`;
      if (counts.U > 0) result += `${counts.U}U`;
      return result;
    }
    
    // ฟังก์ชันสำหรับสร้าง key ในรูปแบบต่างๆ
    function generateKeys(row) {
      const keys = [];
      const studentId = row[5];
      const session = row[3];
      const workingDate = row[2];
      
      // Key แบบปกติ
      keys.push(`${workingDate}_${session}_${studentId}`);
      
      // ถ้า workingDate เป็น Date object
      if (workingDate instanceof Date) {
        // Key แบบ ISO string
        const isoDate = workingDate.toISOString().split('T')[0];
        keys.push(`${isoDate}_${session}_${studentId}`);
        
        // Key แบบ MM/DD/YYYY
        const month = workingDate.getMonth() + 1;
        const day = workingDate.getDate();
        const year = workingDate.getFullYear();
        keys.push(`${month}/${day}/${year}_${session}_${studentId}`);
      }
      
      // ถ้า studentId เป็นตัวเลข
      const numericStudentId = parseInt(studentId);
      if (!isNaN(numericStudentId)) {
        // Key แบบปกติแต่ใช้ studentId เป็นตัวเลข
        keys.push(`${workingDate}_${session}_${numericStudentId}`);
        
        // ถ้า workingDate เป็น Date object
        if (workingDate instanceof Date) {
          // Key แบบ ISO string แต่ใช้ studentId เป็นตัวเลข
          const isoDate = workingDate.toISOString().split('T')[0];
          keys.push(`${isoDate}_${session}_${numericStudentId}`);
          
          // Key แบบ MM/DD/YYYY แต่ใช้ studentId เป็นตัวเลข
          const month = workingDate.getMonth() + 1;
          const day = workingDate.getDate();
          const year = workingDate.getFullYear();
          keys.push(`${month}/${day}/${year}_${session}_${numericStudentId}`);
        }
      }
      
      return keys;
    }
    
    // สร้างข้อมูลเพิ่มเติม
    const additionalData = {};
    
    allRelevantRows.forEach((row, index) => {
      try {
        // นับจำนวน E, S, P, U จากคอลัมน์ที่กำหนด
        // คอลัมน์ L-U คือ index 11-20
        // คอลัมน์ X-AA คือ index 23-26
        // คอลัมน์ AE-AK คือ index 30-36
        // คอลัมน์ AN-AR คือ index 39-43
        // คอลัมน์ AV-AX คือ index 47-49
        // คอลัมน์ BA คือ index 52
        const scoreCounts = countScoreTypes(row, [
          [11, 20], // L-U
          [23, 26], // X-AA
          [30, 36], // AE-AK
          [39, 43], // AN-AR
          [47, 49], // AV-AX
          [52, 52]  // BA
        ]);
        
        // สร้าง scoreSum string
        const scoreSum = formatScoreSum(scoreCounts);
        
        // ดึงข้อมูล Red card และ Yellow card
        // คอลัมน์ H คือ index 7
        // คอลัมน์ J คือ index 9
        const redCards = row[7] ? parseInt(row[7]) || 0 : 0;
        const yellowCards = row[9] ? parseInt(row[9]) || 0 : 0;
        
        // สร้าง key ในรูปแบบต่างๆ
        const keys = generateKeys(row);
        
        // เก็บข้อมูลในทุกรูปแบบ key
        const data = {
          scoreSum: scoreSum,
          redCards: redCards,
          yellowCards: yellowCards
        };
        
        keys.forEach(key => {
          additionalData[key] = data;
        });
        
        // Log ข้อมูลสำหรับการตรวจสอบ
        Logger.log(`Row ${index}: keys=${keys.join(', ')}, scoreSum=${scoreSum}, redCards=${redCards}, yellowCards=${yellowCards}`);
      } catch (error) {
        Logger.log('Error processing row for additional data: ' + error.toString());
      }
    });
    
    Logger.log('Additional data keys: ' + Object.keys(additionalData).length);
    
    return additionalData;
    
  } catch (error) {
    Logger.log('Error in getAdditionalScoreData: ' + error.toString());
    Logger.log('Stack trace: ' + error.stack);
    throw error;
  }
}

function deleteScores(scoreIds) {
  try {
    // ใช้ SpreadsheetApp.openById แทน getActiveSpreadsheet
    const MAIN_SPREADSHEET_ID = "148mOqTFdHSDQg30sorlKRHKjw49GWdlLfItEns3-ddE";
    const ss = SpreadsheetApp.openById(MAIN_SPREADSHEET_ID);
    
    // เปลี่ยนจาก Sheet1 เป็น Sheet2
    const sheet = ss.getSheetByName('Sheet2');
    const dataRange = sheet.getDataRange();
    const data = dataRange.getValues();
    
    // Get current user email
    const userEmail = Session.getActiveUser().getEmail();
    Logger.log('Current user email: ' + userEmail);
    Logger.log('Score IDs to delete: ' + JSON.stringify(scoreIds));
    
    // First, find all rows that belong to the current user and have student IDs
    const userRows = data.map((row, index) => ({
      rowIndex: index,
      email: row[1],
      studentId: row[5],
      studentName: row[4],
      session: row[3],
      work: row[6],
      workingDate: row[2]
    }))
    .filter(row => 
      row.rowIndex > 0 && // Skip header
      row.email === userEmail && 
      row.studentId !== null && 
      row.studentId !== undefined && 
      row.studentId.toString().trim() !== ''
    );
    
    Logger.log('Found user rows: ' + JSON.stringify(userRows));
    
    // Now process the deletion
    let deletedCount = 0;
    let deletedDetails = [];
    
    scoreIds.forEach(id => {
      // Arrays are 0-based, but id starts from 1
      const rowToDelete = userRows[id - 1];
      if (rowToDelete) {
        // Add 1 because sheet rows are 1-based
        const sheetRow = rowToDelete.rowIndex + 1;
        Logger.log('Clearing student ID in row: ' + sheetRow);
        
        // เก็บข้อมูลที่จะลบไว้ในรายละเอียด
        deletedDetails.push({
          studentId: rowToDelete.studentId,
          studentName: rowToDelete.studentName,
          session: rowToDelete.session,
          work: rowToDelete.work,
          workingDate: rowToDelete.workingDate
        });
        
        // Column F is 6
        sheet.getRange(sheetRow, 6).clearContent();
        deletedCount++;
      }
    });
    
    // บันทึกการลบข้อมูล
    accessLog('ลบข้อมูล', {
      count: deletedCount,
      details: deletedDetails
    });
    
    Logger.log('Successfully cleared ' + deletedCount + ' student IDs');
    
    return {
      success: true,
      deletedCount: deletedCount
    };
    
  } catch (error) {
    Logger.log('Error in deleteScores: ' + error.toString());
    Logger.log('Stack trace: ' + error.stack);
    throw error;
  }
}

/**
 * บันทึกการเข้าใช้งานและการทำงานในระบบ
 * @param {string} action - การกระทำที่ต้องการบันทึก
 * @param {object} details - รายละเอียดเพิ่มเติม (ถ้ามี)
 */
function accessLog(action, details) {
  try {
    // ใช้ SpreadsheetApp.openById แทน getActiveSpreadsheet
    const LOG_SPREADSHEET_ID = "1I30s3w4WBevKUJSPraHJz4Q1mPe8ue89oEiJkyAVI_Y";
    const ss = SpreadsheetApp.openById(LOG_SPREADSHEET_ID);
    
    // ตรวจสอบว่ามีชีท "AccessLog" หรือไม่ ถ้าไม่มีให้สร้างใหม่
    let logSheet = ss.getSheetByName('AccessLog');
    if (!logSheet) {
      logSheet = ss.insertSheet('AccessLog');
      
      // สร้างหัวตาราง
      logSheet.getRange('A1:E1').setValues([['Timestamp', 'User', 'Email', 'Action', 'Details']]);
      logSheet.getRange('A1:E1').setFontWeight('bold');
      logSheet.setFrozenRows(1);
      
      // จัดรูปแบบคอลัมน์
      logSheet.setColumnWidth(1, 180); // Timestamp
      logSheet.setColumnWidth(2, 150); // User
      logSheet.setColumnWidth(3, 200); // Email
      logSheet.setColumnWidth(4, 150); // Action
      logSheet.setColumnWidth(5, 400); // Details
    }
    
    // ข้อมูลผู้ใช้
    const user = Session.getActiveUser();
    const email = user.getEmail();
    const username = email.split('@')[0];
    
    // ข้อมูลเพิ่มเติม
    const timestamp = new Date();
    
    // แปลงรายละเอียดเป็น string
    let detailsStr = '';
    if (details) {
      if (typeof details === 'object') {
        try {
          if (action === 'ลบข้อมูล') {
            // สร้างข้อความรายละเอียดสำหรับการลบข้อมูล
            detailsStr = `จำนวน ${details.count} รายการ\n`;
            
            if (details.details && details.details.length > 0) {
              details.details.forEach((item, index) => {
                const dateStr = item.workingDate instanceof Date 
                  ? Utilities.formatDate(item.workingDate, Session.getScriptTimeZone(), 'dd/MM/yyyy')
                  : item.workingDate;
                
                detailsStr += `${index + 1}. ${item.studentName} (${item.studentId}) - ${item.session} - ${dateStr} - ${item.work}\n`;
              });
            }
          } else {
            // กรณีอื่นๆ แปลงเป็น JSON
            detailsStr = JSON.stringify(details);
          }
        } catch (e) {
          detailsStr = 'Error parsing details: ' + e.toString();
        }
      } else {
        detailsStr = details.toString();
      }
    }
    
    // เพิ่มข้อมูลลงในชีท
    logSheet.appendRow([
      timestamp,
      username,
      email,
      action,
      detailsStr
    ]);
    
    Logger.log(`บันทึกการทำงาน: ${action} โดย ${email}`);
    
  } catch (error) {
    Logger.log('Error in accessLog: ' + error.toString());
    Logger.log('Stack trace: ' + error.stack);
    // ไม่ throw error เพื่อไม่ให้กระทบการทำงานหลัก
  }
}