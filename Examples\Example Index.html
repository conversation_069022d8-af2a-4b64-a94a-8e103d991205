<!DOCTYPE html>
<html>
  <head>
    <base target="_top">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.0/css/all.min.css" rel="stylesheet">
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              primary: {
                DEFAULT: '#3b82f6',
                foreground: '#ffffff',
              },
              destructive: {
                DEFAULT: '#ef4444',
                foreground: '#ffffff',
              },
              muted: {
                DEFAULT: '#f3f4f6',
                foreground: '#6b7280',
              },
            },
          },
        },
      }
    </script>
    <style type="text/tailwindcss">
      @layer components {
        .checkbox {
          @apply h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500 checked:bg-red-600 checked:hover:bg-red-600;
        }
        
        .badge {
          @apply inline-flex items-center rounded-lg px-2.5 py-1 text-xs font-medium ring-1 ring-inset;
        }
        
        .avatar-circle {
          @apply w-6 h-6 rounded-full bg-gray-400 flex items-center justify-center text-xs font-light text-white;
        }
        
        .badge-morning {
          @apply bg-blue-50 text-blue-700 ring-blue-600/20;
        }
        
        .badge-afternoon {
          @apply bg-amber-50 text-amber-700 ring-amber-600/20;
        }
        
        .badge-evening {
          @apply bg-purple-50 text-purple-700 ring-purple-600/20;
        }
        
        .count-badge {
          @apply bg-gray-50 text-gray-700 ring-gray-600/20;
        }
        
        .badge-work {
          @apply bg-gray-100 text-gray-700 ring-gray-600/20;
        }
        
        .badge-green {
          @apply bg-green-50 text-green-700 ring-green-600/20;
        }
        
        .badge-pink {
          @apply bg-pink-50 text-pink-700 ring-pink-600/20;
        }
        
        .badge-indigo {
          @apply bg-indigo-50 text-indigo-700 ring-indigo-600/20;
        }
        
        .badge-yellow {
          @apply bg-yellow-50 text-yellow-700 ring-yellow-600/20;
        }
        
        .badge-red {
          @apply bg-red-50 text-red-700 ring-red-600/20;
        }
        
        .badge-blue {
          @apply bg-blue-50 text-blue-700 ring-blue-600/20;
        }
        
        .badge-purple {
          @apply bg-purple-50 text-purple-700 ring-purple-600/20;
        }
        
        .badge-teal {
          @apply bg-teal-50 text-teal-700 ring-teal-600/20;
        }
        
        .spinner {
          @apply inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent align-[-0.125em];
        }
        
        .spinner-sm {
          @apply h-3 w-3;
        }
        
        .spinner-lg {
          @apply h-8 w-8;
        }
        
        .toast {
          @apply fixed bottom-4 right-4 z-50 rounded-lg bg-white px-6 py-4 shadow-lg ring-1 ring-black/5;
        }
        
        .toast-success {
          @apply bg-green-50 text-green-800 ring-green-500/10;
        }
        
        .badge-stat {
          @apply inline-flex items-center text-xs text-gray-500 border border-gray-200 rounded-full px-2 py-0.5 bg-transparent;
        }
        
        /* เพิ่ม style สำหรับ scoreSum และ redyellowSum */
        .score-sum {
          @apply text-xs text-gray-500 font-normal ml-1;
        }
        
        .red-yellow-sum {
          @apply text-xs text-gray-500 ml-1 flex items-center;
        }
        
        .red-dot {
          @apply w-2 h-2 rounded-full bg-red-500 mr-0.5;
        }
        
        .yellow-dot {
          @apply w-2 h-2 rounded-full bg-yellow-500 mr-0.5;
        }
        
        /* เพิ่ม style สำหรับ dot loading */
        .dot-loading {
          display: inline-block;
          position: relative;
          width: 20px;
          text-align: left;
        }
        
        .dot-loading:after {
          content: '.';
          animation: dots 1.5s steps(3, end) infinite;
        }
        
        @keyframes dots {
          0%, 20% { content: '.'; }
          40%, 60% { content: '..'; }
          80%, 100% { content: '...'; }
        }
        
        /* เพิ่ม style สำหรับ help icon และ modal */
        .help-icon {
          @apply ml-1 text-gray-400 hover:text-gray-600 cursor-pointer transition-colors;
        }
        
        .help-icon-pulse {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: .8;
            transform: scale(1.1);
          }
        }
        
        .modal-backdrop {
          @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity;
        }
        
        .modal-bottom-sheet {
          @apply fixed bottom-0 left-0 right-0 bg-white rounded-t-xl shadow-xl z-50 transform transition-transform duration-300 ease-in-out;
          max-height: 90vh;
          overflow-y: auto;
        }
        
        .modal-bottom-sheet.hidden {
          transform: translateY(100%);
        }
        
        .modal-bottom-sheet.visible {
          transform: translateY(0);
        }
        
        .modal-drag-handle {
          @apply w-12 h-1.5 bg-gray-300 rounded-full mx-auto my-3;
        }
        
        .info-section {
          @apply border-b border-gray-200 p-4;
        }
        
        .info-section:last-child {
          @apply border-b-0;
        }
        
        .info-title {
          @apply text-sm font-medium text-gray-700 mb-2;
        }
        
        .info-content {
          @apply text-xs text-gray-600;
        }
        
        .icon-legend {
          @apply flex items-center gap-2 py-1;
        }
        
        .remover-gradient {
          @apply bg-clip-text text-transparent bg-gradient-to-r from-gray-500 to-gray-200 inline-block font-light;
        }
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen">
    <header class="border-b bg-white">
      <div class="container mx-auto px-4 py-4">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div class="flex justify-between items-center">
            <div class="flex flex-col">
              <h1 class="text-2xl font-bold text-gray-900">
                <i class="fa-solid fa-eraser mr-2"></i>Daily<span class="remover-gradient">Remover</span>
              </h1>
              <p class="text-sm text-gray-500 md:block hidden mt-1">ระบบลบคะแนน Daily Performance</p>
            </div>
            <div class="text-right text-xs text-gray-500 md:hidden">
              <div>ระบบลบคะแนน</div>
              <div>Daily Performance</div>
            </div>
          </div>
          <div class="flex flex-col items-start md:items-end mt-2 md:mt-0">
            <div class="md:border-t-0 pt-2 md:pt-0 w-full">
              <div class="flex items-center text-gray-400 flex-wrap justify-start md:justify-end gap-2 text-left md:text-right">
                <div class="avatar-circle">
                  <span><?= initials ?></span>
                </div>
                <div class="flex gap-2">
                  <div class="badge-stat">
                    <i class="fa-solid fa-calendar-check text-gray-400 mr-1"></i>
                    <span id="sameDayPercentage">-</span>
                  </div>
                  <div class="badge-stat">
                    <i class="fa-solid fa-crosshairs text-gray-400 mr-1"></i>
                    <span id="entryAccuracy">-</span>
                  </div>
                  <div class="help-icon" id="helpButton">
                    <i class="fa-solid fa-circle-question"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
    
    <main class="px-4 py-8">
      <div class="container mx-auto w-full lg:max-w-[75%] space-y-4">
        <div class="bg-white rounded-lg border shadow-sm p-4 mb-4">
          <div class="flex flex-col gap-4">
            <div class="relative flex-1">
              <div class="flex gap-2 items-center">
                <div class="relative flex-1">
                  <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                  <input 
                    id="searchInput" 
                    type="text" 
                    placeholder="Name or ID..." 
                    class="pl-10 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                </div>
                <button 
                  onclick="clearAllFilters()"
                  class="text-xs text-gray-500 hover:text-gray-700 flex flex-col items-center gap-0.5 hidden"
                  id="clearAllFiltersButton"
                >
                  <div class="flex items-center gap-2">
                    <i class="fas fa-times self-center"></i>
                    <div class="text-left leading-none">
                      Clear<br>Filters
                    </div>
                  </div>
                </button>
              </div>
            </div>
            
            <div class="flex justify-end items-center gap-4">
              <div class="flex-1 flex items-center gap-2">
                <button 
                  onclick="toggleErrorFilter('duplicate')"
                  class="badge-stat"
                  id="duplicateFilterBadge"
                >
                  <i class="fa-solid fa-clone text-gray-400 mr-1"></i>
                  <span>ซ้ำ</span>
                </button>
                <button 
                  onclick="toggleErrorFilter('session')"
                  class="badge-stat"
                  id="sessionFilterBadge"
                >
                  <i class="fa-solid fa-calendar-xmark text-gray-400 mr-1"></i>
                  <span>ผิดคาบ</span>
                </button>
              </div>

              <button 
                onclick="toggleFilters()"
                class="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-2 whitespace-nowrap"
                id="advancedFilterButton"
              >
                <i class="fas fa-sliders-h"></i>
                Advanced Filters
                <i id="filterChevron" class="fas fa-chevron-down transition-transform"></i>
              </button>
            </div>
            
            <div id="advancedFilters" class="hidden">
              <div class="border-t pt-4">
                <div class="grid grid-cols-1 gap-4">
                  <!-- First Row: Date and Session on narrow screens -->
                  <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <!-- Date Filter -->
                    <div class="col-span-1">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                      <div class="relative">
                        <input 
                          type="date" 
                          id="workDateFilter"
                          class="w-full rounded-md border border-input bg-background px-3 pr-8 py-[0.4rem] text-sm transition-colors"
                        >
                        <button 
                          onclick="clearDateFilter()"
                          class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          style="display: none;"
                          id="clearDateButton"
                        >
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                    </div>

                    <!-- Session Filter -->
                    <div class="col-span-1">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Session</label>
                      <select 
                        id="sessionFilter" 
                        class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm transition-colors"
                      >
                        <option value="all">ทุกคาบ</option>
                        <option value="Morning">เช้า</option>
                        <option value="Afternoon">บ่าย</option>
                        <option value="Evening">เย็น</option>
                      </select>
                    </div>

                    <!-- Work Filter -->
                    <div class="col-span-2 md:col-span-1">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Work</label>
                      <select 
                        id="workFilter" 
                        class="w-full rounded-md border border-input bg-background px-3 py-2 text-sm transition-colors"
                      >
                        <option value="all">ทั้งหมด</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-between items-center mb-4">
          <button 
            onclick="clearSelection()" 
            class="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1 hidden cursor-pointer"
            id="selectionCount"
          >
            <i class="fas fa-xmark"></i>
            <span></span>
          </button>
          <div class="flex gap-2 ml-auto">
            <button 
              id="refreshButton" 
              class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-white hover:bg-gray-50 h-9 px-4 py-2 text-gray-500"
            >
              <i class="fas fa-sync-alt mr-2 text-gray-500"></i>
              Refresh
            </button>
            
            <button 
              id="deleteButton" 
              class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive text-destructive-foreground hover:bg-destructive/90 h-9 px-4 py-2 w-[140px] whitespace-nowrap"
              disabled
            >
              <i class="fas fa-trash-alt mr-2"></i>
              Delete Selected
            </button>
          </div>
        </div>
        
        <div id="scoreGroups" class="space-y-2">
          <div class="text-center p-8 border rounded-md">
            <div class="flex flex-col items-center gap-3">
              <div class="spinner spinner-lg text-gray-400"></div>
              <div class="text-gray-500" id="loadingMessage">Beginning Check...</div>
            </div>
          </div>
        </div>
        
        <div id="entriesCount" class="text-sm text-gray-500">
          Showing 0 of 0 entries
        </div>
        <div class="text-center text-xs text-gray-400 mt-8">
          taksid.c, kunchorn.k and DentRSU DevTeam
        </div>
      </div>
      
      <!-- Delete Confirmation Dialog -->
      <div id="deleteDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg max-w-md w-full p-6 shadow-lg min-h-[300px] flex flex-col">
          <div class="text-center mb-4">
            <h3 class="text-lg font-bold">Recheck Deletion</h3>
          </div>
          <div id="deleteConfirmContent" class="flex-1">
            <div class="text-center mb-4 text-gray-700">
              ท่านต้องการลบ <span id="selectedCount"></span> รายการ
            </div>
            <div class="text-gray-600 mb-4">
              <div id="deleteConfirmMessage" class="space-y-2">
              </div>
            </div>
            <div class="text-center text-red-600 text-sm">
              ลบข้อมูลแล้วไม่สามารถกู้คืนได้
            </div>
          </div>
          <div id="deleteSuccess" class="hidden flex-1 flex items-center justify-center">
            <div class="flex flex-col items-center gap-4">
              <i class="fas fa-check-circle text-5xl text-green-600"></i>
              <span class="text-green-600">Daily removal completed</span>
            </div>
          </div>
          <div class="flex justify-center gap-2 mt-4">
            <button 
              id="cancelDelete" 
              class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
            >
              <span>Cancel</span>
              <span id="closeTimer" class="hidden ml-1"></span>
            </button>
            <button 
              id="confirmDelete" 
              class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive text-destructive-foreground hover:bg-destructive/90 h-9 px-4 py-2"
            >
              <i id="deleteSpinner" class="spinner spinner-sm text-white mr-2 hidden"></i>
              Delete
            </button>
          </div>
        </div>
      </div>
    </main>

    <!-- Help Modal Bottom Sheet -->
    <div id="helpModal" class="modal-backdrop hidden">
      <div id="helpBottomSheet" class="modal-bottom-sheet hidden">
        <div class="modal-drag-handle"></div>
        
        <div class="p-4">
          <div class="flex justify-between items-center mb-2">
            <h3 class="text-lg font-medium">Info</h3>
            <button id="closeHelpModal" class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
          
          <!-- Icon Legend Section -->
          <div class="info-section">
            
            <div class="info-content space-y-1">
              <div class="icon-legend">
                <i class="fa-regular fa-clone text-red-500 text-xs"></i>
                <span>เดลี่ซ้ำ ระบบใช้เดลี่แรกที่เข้าระบบ การแก้ไขคะแนนต้องลบเดลี่แรกแล้วให้คะแนนใหม่</span>
              </div>
              <div class="icon-legend">
                <i class="fa-solid fa-user-group text-red-500 text-xs"></i>
                <span>ซ้ำกับอาจารย์ท่านอื่น โปรดแจ้ง นทพ. ให้ติดต่ออาจารย์เพื่อลบข้อมูล</span>
              </div>
              <div class="icon-legend">
                <i class="fa-solid fa-ban text-red-500 text-xs"></i>
                <span>คาบไม่มีคลินิก โปรดลบและให้คะแนนใหม่</span>
              </div>
              <div class="icon-legend">
                <i class="fa-regular fa-calendar-plus text-red-500 text-xs"></i>
                <span>เดลี่ก่อนวันปฏิบัติงาน โปรดลบและให้คะแนนใหม่</span>
              </div>
              <div class="icon-legend">
                <i class="fa-solid fa-calendar-plus text-green-500 text-xs"></i>
                <span>คาบคลินิกเพิ่มเติม นอกเหนือจากตารางปกติ</span>
              </div>
              <div class="icon-legend">
                <i class="fas fa-exclamation text-red-500"></i>
                <span>พบรายการที่มีข้อผิดพลาด</span>
              </div>
              <div class="icon-legend">
                <span class="red-yellow-sum">
                  <span class="flex items-center"><span class="red-dot"></span></span>
                  <span class="flex items-center ml-1"><span class="yellow-dot"></span></span>
                </span>
                <span>ใบแดง/เหลือง</span>
              </div>
            </div>
          </div>
          
          <!-- Stats Explanation Section -->
          <div class="info-section">
         <div class="info-content space-y-1">
           <div class="icon-legend">
             <i class="fa-solid fa-calendar-check text-blue-500 mt-0.5"></i>
             <span>% การให้คะแนนในวันเดียวกับวันที่ปฏิบัติงาน</span>
           </div>
           <div class="icon-legend">
             <i class="fa-solid fa-crosshairs text-green-500 mt-0.5"></i>
             <span>% ความถูกต้องของการป้อนคะแนน คำนวณจากจำนวนเดลี่ที่ไม่มีข้อผิดพลาด</span>
           </div>
            </div>
          </div>

          <!-- Usage Info Section -->
          <div class="info-section">
            <div class="info-title">วิธีใช้งาน</div>
            <div class="info-content space-y-2">
              <p>1. เลื่อนหาหรือค้นหา นทพ.โดยใช้ชื่อหรือ ID</p>
              <p>2. สามารถกด Advanced Filters เพื่อกรองข้อมูล</p>
              <p>3. เลือกเดลี่ที่ต้องการลบโดยคลิกที่ checkbox</p>
              <p>4. ยกเลิกการเลือกทั้งหมด กดที่ "x รายการ"</p>
              <p>5. กดปุ่ม "Delete Selected" เพื่อลบเดลี่ที่เลือก</p>
              <p>6. ยืนยันการลบในหน้าต่างที่ปรากฏ</p>
              <p>"ท่านจะสามารถลบเดลี่ที่ท่านเป็นผู้ให้ได้เท่านั้น"</p>
            </div>
          </div>
          
          <div class="text-center text-xs text-gray-400 mt-4 mb-2">
            DailyRemover v3.1 - <EMAIL>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global variables
      let allScores = [];
      let filteredScores = [];
      let selectedScores = [];
      let expandedDates = [];
      let uniqueWorkTypes = [];
      let isLoadingAdditionalData = false; // เพิ่มตัวแปรสำหรับตรวจสอบสถานะการโหลดข้อมูลเพิ่มเติม
      
      // เพิ่มตัวแปร flag สำหรับเปิด/ปิดการโหลดข้อมูลเพิ่มเติม
      const ENABLE_ADDITIONAL_DATA = true; // เปลี่ยนเป็น true เพื่อเปิดใช้งาน
      
      // Session icons mapping
      const sessionIcons = {
        'Morning': 'fa-sun',
        'Afternoon': 'fa-cloud-sun',
        'Evening': 'fa-moon'
      };
      
      // Session badge classes
      const sessionClasses = {
        'Morning': 'badge-morning',
        'Afternoon': 'badge-afternoon',
        'Evening': 'badge-evening'
      };
      
      // Session display text mapping
      const sessionDisplayText = {
        'Morning': 'เช้า',
        'Afternoon': 'บ่าย',
        'Evening': 'เย็น',
        'Red or Yellow card only': 'Red/Yellow card'
      };
      
      // Initialize the app when the page loads
      document.addEventListener('DOMContentLoaded', function() {
        initializeApp();
        
        // เพิ่ม pulse animation เมื่อโหลดหน้าเว็บ
        const helpButton = document.getElementById('helpButton');
        helpButton.classList.add('help-icon-pulse');
        
        // หยุด animation หลังจาก 30 วินาที
        setTimeout(() => {
          helpButton.classList.remove('help-icon-pulse');
        }, 30000);
      });
      
      function initializeApp() {
        loadScoreData(false, false);
        
        // Event Listeners
        document.getElementById('refreshButton').addEventListener('click', function() {
          loadScoreData(false, true);
        });
        
        // Debounce search input
        const searchInput = document.getElementById('searchInput');
        let searchTimeout;
        searchInput.addEventListener('input', () => {
          clearTimeout(searchTimeout);
          searchTimeout = setTimeout(filterScores, 300);
        });
        
        // Filter change events
        document.getElementById('sessionFilter').addEventListener('change', filterScores);
        document.getElementById('workFilter').addEventListener('change', filterScores);
        
        // Date filter with clear button
        const dateFilter = document.getElementById('workDateFilter');
        const clearDateButton = document.getElementById('clearDateButton');
        
        dateFilter.addEventListener('change', () => {
          clearDateButton.style.display = dateFilter.value ? 'block' : 'none';
          filterScores();
        });
        
        document.getElementById('deleteButton').addEventListener('click', showDeleteDialog);
        document.getElementById('cancelDelete').addEventListener('click', hideDeleteDialog);
        document.getElementById('confirmDelete').addEventListener('click', deleteSelectedScores);
        
        // Help Modal
        const helpButton = document.getElementById('helpButton');
        const helpModal = document.getElementById('helpModal');
        const helpBottomSheet = document.getElementById('helpBottomSheet');
        const closeHelpModal = document.getElementById('closeHelpModal');
        
        helpButton.addEventListener('click', () => {
          helpModal.classList.remove('hidden');
          setTimeout(() => {
            helpBottomSheet.classList.remove('hidden');
            helpBottomSheet.classList.add('visible');
          }, 10);
        });
        
        function closeModal() {
          helpBottomSheet.classList.remove('visible');
          setTimeout(() => {
            helpBottomSheet.classList.add('hidden');
            helpModal.classList.add('hidden');
          }, 300);
        }
        
        closeHelpModal.addEventListener('click', closeModal);
        
        helpModal.addEventListener('click', (e) => {
          if (e.target === helpModal) {
            closeModal();
          }
        });
      }
      
      function loadScoreData(isAfterDelete = false, isRefresh = false) {
        // Show loading state
        document.getElementById('scoreGroups').innerHTML = `
          <div class="text-center p-8 border rounded-md bg-white">
            <div class="flex flex-col items-center gap-3">
              <div class="spinner spinner-lg text-gray-400"></div>
              <div class="text-gray-500" id="loadingMessage">${
                isAfterDelete ? 'Polishing Daily Data...' : 
                isRefresh ? 'Probing Data Changes...' : 
                'Beginning Check...'
              }</div>
            </div>
          </div>
        `;
        
        // Add loading message animation
        setTimeout(() => {
          const loadingMessage = document.getElementById('loadingMessage');
          if (loadingMessage && !isAfterDelete && !isRefresh) {
            loadingMessage.textContent = 'Daily Preparation...';
          }
        }, 2000);
        
        document.getElementById('refreshButton').disabled = true;
        
        console.log('Fetching score data...');
        
        // Call the server-side function to get data
        google.script.run
          .withSuccessHandler(function(response) {
            console.log('Data received:', response);
            
            // Check if response has the expected structure
            if (!response || !response.scores || !Array.isArray(response.scores)) {
              console.error('Received data is not in the expected format:', response);
              document.getElementById('scoreGroups').innerHTML = 
                '<div class="text-center p-8 border rounded-md text-red-500">Error: Invalid data format received from server</div>';
              document.getElementById('refreshButton').disabled = false;
              return;
            }

            // Update statistics display
            if (response.stats) {
              document.getElementById('sameDayPercentage').textContent = 
                `${response.stats.sameDayPercentage}% Same-day`;
              document.getElementById('entryAccuracy').textContent = 
                `${response.stats.entryAccuracy}% Accuracy`;
            }

            // Transform dates to proper format and sort by date (newest first)
            const data = response.scores
              .map(item => ({
                ...item,
                timestamp: new Date(item.timestamp).toISOString(),
                workingDate: new Date(Date.parse(item.workingDate)).toISOString()
              }))
              .sort((a, b) => new Date(b.workingDate) - new Date(a.workingDate));
            
            console.log('Transformed data:', data);
            
            allScores = data;
            document.getElementById('refreshButton').disabled = false;
            
            // Extract unique work types for the filter
            updateWorkFilter();
            
            // Set the newest date as expanded by default
            if (data.length > 0) {
              const newestDate = new Date(data[0].workingDate).toISOString().split('T')[0];
              expandedDates = [newestDate];
            }
            
            filterScores();
            
            // โหลดข้อมูลเพิ่มเติมหลังจากแสดงข้อมูลพื้นฐานแล้ว
            loadAdditionalData();
          })
          .withFailureHandler(function(error) {
            console.error('Error loading data:', error);
            document.getElementById('scoreGroups').innerHTML = 
              '<div class="text-center p-8 border rounded-md text-red-500">Error loading data: ' + error.message + '<br>Please check if you are logged in with the correct account.</div>';
            document.getElementById('refreshButton').disabled = false;
          })
          .getScoreData();
      }
      
      /**
       * ฟังก์ชันสำหรับโหลดข้อมูลเพิ่มเติม (scoreSum และ redyellowSum)
       */
      function loadAdditionalData() {
        console.log('Loading additional data...');
        
        // ตรวจสอบ flag ว่าเปิดใช้งานหรือไม่
        if (!ENABLE_ADDITIONAL_DATA) {
          console.log('Additional data loading is disabled');
          return;
        }
        
        // ตั้งค่าสถานะการโหลดข้อมูลเพิ่มเติม
        isLoadingAdditionalData = true;
        
        // แสดง placeholder สำหรับ scoreSum
        renderScores();
        
        // เรียกใช้ฟังก์ชัน getAdditionalScoreData() ที่ฝั่ง server
        google.script.run
          .withSuccessHandler(function(additionalData) {
            console.log('Additional data received:', additionalData);
            
            // ตรวจสอบว่ามีข้อมูลหรือไม่
            if (!additionalData || Object.keys(additionalData).length === 0) {
              console.error('No additional data received');
              isLoadingAdditionalData = false;
              return;
            }
            
            // แสดงตัวอย่างข้อมูลที่ได้รับ
            const sampleKeys = Object.keys(additionalData).slice(0, 3);
            sampleKeys.forEach(key => {
              console.log('Sample data:', key, additionalData[key]);
            });
            
            // นับจำนวน scores ที่อัปเดตได้
            let updatedCount = 0;
            
            // อัปเดตข้อมูล scoreSum และ redyellowSum ในแต่ละ score
            allScores.forEach(score => {
              try {
                // สร้าง key สำหรับการอ้างอิงข้อมูล
                // แปลงวันที่ให้อยู่ในรูปแบบเดียวกับฝั่ง server
                const workingDate = new Date(score.workingDate);
                let dateKey;
                
                // ถ้าเป็น Date object
                if (workingDate instanceof Date && !isNaN(workingDate)) {
                  // ใช้รูปแบบ MM/DD/YYYY เหมือนกับฝั่ง server
                  const month = workingDate.getMonth() + 1;
                  const day = workingDate.getDate();
                  const year = workingDate.getFullYear();
                  dateKey = `${month}/${day}/${year}`;
                } else {
                  // ถ้าไม่ใช่ Date object ให้ใช้ค่าเดิม
                  dateKey = score.workingDate;
                }
                
                const key = `${dateKey}_${score.session}_${score.studentId}`;
                console.log('Looking for key:', key);
                
                // ถ้ามีข้อมูลเพิ่มเติมสำหรับ score นี้
                if (additionalData[key]) {
                  console.log('Found data for key:', key, additionalData[key]);
                  score.scoreSum = additionalData[key].scoreSum;
                  score.redCards = additionalData[key].redCards;
                  score.yellowCards = additionalData[key].yellowCards;
                  updatedCount++;
                } else {
                  console.log('No data found for key:', key);
                  
                  // ลองค้นหาด้วย key อื่น
                  const alternativeKey = `${new Date(score.workingDate).toISOString().split('T')[0]}_${score.session}_${score.studentId}`;
                  console.log('Trying alternative key:', alternativeKey);
                  
                  if (additionalData[alternativeKey]) {
                    console.log('Found data for alternative key:', alternativeKey, additionalData[alternativeKey]);
                    score.scoreSum = additionalData[alternativeKey].scoreSum;
                    score.redCards = additionalData[alternativeKey].redCards;
                    score.yellowCards = additionalData[alternativeKey].yellowCards;
                    updatedCount++;
                  } else {
                    // ลองค้นหาด้วย key ที่ใช้ studentId เป็นตัวเลข
                    const numericStudentId = parseInt(score.studentId);
                    if (!isNaN(numericStudentId)) {
                      const numericKey = `${dateKey}_${score.session}_${numericStudentId}`;
                      console.log('Trying numeric key:', numericKey);
                      
                      if (additionalData[numericKey]) {
                        console.log('Found data for numeric key:', numericKey, additionalData[numericKey]);
                        score.scoreSum = additionalData[numericKey].scoreSum;
                        score.redCards = additionalData[numericKey].redCards;
                        score.yellowCards = additionalData[numericKey].yellowCards;
                        updatedCount++;
                      }
                    }
                  }
                }
              } catch (error) {
                console.error('Error updating score:', error);
              }
            });
            
            console.log(`Updated ${updatedCount} out of ${allScores.length} scores`);
            
            // อัปเดตข้อมูลใน filteredScores ด้วย
            filteredScores = allScores.filter(score => filteredScores.some(fs => fs.id === score.id));
            
            // ตั้งค่าสถานะการโหลดข้อมูลเพิ่มเติมเป็น false
            isLoadingAdditionalData = false;
            
            // อัปเดตการแสดงผล
            renderScores();
            
            // อัปเดตฟิลเตอร์เพื่อให้รองรับการค้นหาด้วย scoreSum
            filterScores();
          })
          .withFailureHandler(function(error) {
            console.error('Error loading additional data:', error);
            
            // ตั้งค่าสถานะการโหลดข้อมูลเพิ่มเติมเป็น false
            isLoadingAdditionalData = false;
          })
          .getAdditionalScoreData();
      }
      
      function updateWorkFilter() {
        // Get unique work types
        uniqueWorkTypes = [...new Set(allScores.map(score => score.work))].filter(Boolean).sort();
        
        // Update the work filter dropdown
        const workFilter = document.getElementById('workFilter');
        
        // Keep the default "ทั้งหมด" option
        workFilter.innerHTML = '<option value="all">ทั้งหมด</option>';
        
        // Add options for each unique work type
        uniqueWorkTypes.forEach(workType => {
          const option = document.createElement('option');
          option.value = workType;
          option.textContent = getShortWorkText(workType);
          workFilter.appendChild(option);
        });
      }
      
      function updateFilterHighlights() {
        const searchTerm = document.getElementById('searchInput').value;
        const sessionFilter = document.getElementById('sessionFilter');
        const workFilter = document.getElementById('workFilter');
        const dateFilter = document.getElementById('workDateFilter');
        const advancedFilterButton = document.getElementById('advancedFilterButton');
        const clearAllFiltersButton = document.getElementById('clearAllFiltersButton');
        
        // Check if any filter is active
        const hasActiveFilters = searchTerm !== '' || 
                               sessionFilter.value !== 'all' || 
                               workFilter.value !== 'all' || 
                               dateFilter.value !== '';
        
        // Show/hide clear all filters button
        clearAllFiltersButton.classList.toggle('hidden', !hasActiveFilters);
        
        // Highlight search input
        if (searchTerm) {
          document.getElementById('searchInput').classList.add('ring-2', 'ring-blue-500', 'border-blue-500');
        } else {
          document.getElementById('searchInput').classList.remove('ring-2', 'ring-blue-500', 'border-blue-500');
        }
        
        // Highlight session filter
        if (sessionFilter.value !== 'all') {
          sessionFilter.classList.add('ring-2', 'ring-blue-500', 'border-blue-500');
        } else {
          sessionFilter.classList.remove('ring-2', 'ring-blue-500', 'border-blue-500');
        }
        
        // Highlight work filter
        if (workFilter.value !== 'all') {
          workFilter.classList.add('ring-2', 'ring-blue-500', 'border-blue-500');
        } else {
          workFilter.classList.remove('ring-2', 'ring-blue-500', 'border-blue-500');
        }
        
        // Highlight date filter
        if (dateFilter.value) {
          dateFilter.classList.add('ring-2', 'ring-blue-500', 'border-blue-500');
        } else {
          dateFilter.classList.remove('ring-2', 'ring-blue-500', 'border-blue-500');
        }
        
        // Highlight advanced filter button if any filter is active
        if (sessionFilter.value !== 'all' || workFilter.value !== 'all' || dateFilter.value) {
          advancedFilterButton.classList.add('text-blue-600', 'font-medium');
        } else {
          advancedFilterButton.classList.remove('text-blue-600', 'font-medium');
        }
      }
      
      let activeErrorFilters = new Set();

      function toggleErrorFilter(type) {
        const badge = document.getElementById(`${type}FilterBadge`);
        
        if (activeErrorFilters.has(type)) {
          // Deactivate
          activeErrorFilters.delete(type);
          badge.classList.remove('bg-gray-200', 'ring-gray-200');
          badge.classList.add('bg-transparent');
          badge.querySelector('i').classList.remove('text-gray-700');
          badge.querySelector('i').classList.add('text-gray-400');
          badge.querySelector('span').classList.remove('text-gray-700');
          badge.querySelector('span').classList.add('text-gray-500');
          
          // ถ้าไม่มีฟิลเตอร์ใดๆ ที่ active อยู่ ให้แสดงเฉพาะวันล่าสุด
          if (activeErrorFilters.size === 0) {
            if (allScores.length > 0) {
              const newestDate = new Date(allScores[0].workingDate).toISOString().split('T')[0];
              expandedDates = [newestDate];
            } else {
              expandedDates = [];
            }
          }
        } else {
          // Activate
          activeErrorFilters.add(type);
          badge.classList.remove('bg-transparent');
          badge.classList.add('bg-gray-200', 'ring-gray-200');
          badge.querySelector('i').classList.remove('text-gray-400');
          badge.querySelector('i').classList.add('text-gray-700');
          badge.querySelector('span').classList.remove('text-gray-500');
          badge.querySelector('span').classList.add('text-gray-700');
        }
        
        filterScores();
      }
      
      function filterScores() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const sessionFilter = document.getElementById('sessionFilter').value;
        const workFilter = document.getElementById('workFilter').value;
        const workDateFilter = document.getElementById('workDateFilter').value || 'all';
        
        const hasActiveFilters = searchTerm !== '' || 
                               sessionFilter !== 'all' || 
                               workFilter !== 'all' || 
                               workDateFilter !== 'all' ||
                               activeErrorFilters.size > 0;
        
        // Update filter highlights
        updateFilterHighlights();
        
        console.log('Filtering with criteria:', {
          searchTerm,
          sessionFilter,
          workFilter,
          workDateFilter,
          activeErrorFilters: Array.from(activeErrorFilters),
          hasActiveFilters
        });
        
        console.log('Total scores before filtering:', allScores.length);
        
        // Filter the scores based on search term and filters
        filteredScores = allScores.filter(score => {
          // ค้นหาจากชื่อ, รหัสนักศึกษา, และ scoreSum
          let matchesSearch = false;
          if (searchTerm === '') {
            matchesSearch = true;
          } else {
            if (score.studentName && score.studentName.toLowerCase().includes(searchTerm)) {
              matchesSearch = true;
            }
            if (score.studentId && score.studentId.toString().toLowerCase().includes(searchTerm)) {
              matchesSearch = true;
            }
            if (score.scoreSum && score.scoreSum.toLowerCase().includes(searchTerm)) {
              matchesSearch = true;
            }
          }
          
          const matchesSession = sessionFilter === 'all' || score.session === sessionFilter;
          const matchesWork = workFilter === 'all' || score.work === workFilter;
          
          // Handle workDateFilter
          let matchesWorkDate = true;
          if (workDateFilter !== 'all') {
            try {
              const scoreDate = new Date(score.workingDate);
              const filterDate = new Date(workDateFilter);
              matchesWorkDate = scoreDate.toISOString().split('T')[0] === workDateFilter;
            } catch (error) {
              console.error('Error comparing dates:', error);
              matchesWorkDate = false;
            }
          }
          
          // Error filters
          let matchesErrorFilters = true;
          if (activeErrorFilters.size > 0) {
            matchesErrorFilters = false;
            if (activeErrorFilters.has('duplicate')) {
              matchesErrorFilters = matchesErrorFilters || score.isDuplicate || score.hasDuplicateWithOtherTeacher;
            }
            if (activeErrorFilters.has('session')) {
              matchesErrorFilters = matchesErrorFilters || score.hasNoClinic || score.isSubmitBeforeWork;
            }
          }
          
          const matches = matchesSearch && matchesSession && matchesWork && matchesWorkDate && matchesErrorFilters;
          
          return matches;
        });
        
        // If there are active filters, expand all dates that have matching scores
        if (hasActiveFilters) {
          const matchedDates = [...new Set(filteredScores.map(score => 
            new Date(score.workingDate).toISOString().split('T')[0]
          ))];
          expandedDates = matchedDates;
        }
        
        console.log('Filtered scores count:', filteredScores.length);
        
        // Update the UI
        renderScores();
        updateDeleteButton();
      }
      
      function renderScores() {
        const scoreGroupsElement = document.getElementById('scoreGroups');
        
        console.log('Rendering scores. Total filtered scores:', filteredScores.length);
        
        // Group scores by working date
        const groupedScores = {};
        filteredScores.forEach(score => {
          try {
            // Format the date to be consistent
            const dateObj = new Date(score.workingDate);
            const formattedDate = dateObj.toISOString().split('T')[0]; // YYYY-MM-DD format
            console.log('Processing score for grouping:', {
              score: score.studentName,
              workingDate: score.workingDate,
              formattedDate
            });
            
            if (!groupedScores[formattedDate]) {
              groupedScores[formattedDate] = [];
            }
            groupedScores[formattedDate].push(score);
          } catch (error) {
            console.error('Error processing score:', score, error);
          }
        });
        
        console.log('Grouped scores:', groupedScores);
        
        // Sort dates in descending order (newest first)
        const sortedDates = Object.keys(groupedScores).sort((a, b) => 
          new Date(b).getTime() - new Date(a).getTime()
        );
        
        console.log('Sorted dates:', sortedDates);
        
        // If no scores found
        if (sortedDates.length === 0) {
          scoreGroupsElement.innerHTML = '<div class="text-center p-8 border rounded-md bg-white">ไม่พบข้อมูล</div>';
          document.getElementById('entriesCount').textContent = 'แสดง 0 จาก ' + allScores.length + ' รายการ';
          return;
        }
        
        // Build the HTML for each date group
        let html = '';
        
        sortedDates.forEach(date => {
          const dateScores = groupedScores[date];
          const isExpanded = expandedDates.includes(date);
          const formattedDate = formatWorkingDate(date);
          
          html += `
            <div class="bg-white rounded-lg border shadow-sm mb-4">
              <div class="flex items-center justify-between p-4 bg-gray-50 cursor-pointer rounded-t-lg" onclick="toggleDateExpansion('${date}')">
                <div class="font-medium flex items-center">
                  <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'} mr-2 text-gray-500"></i>
                  ${formattedDate}
                </div>
                <div class="flex items-center gap-2">
                  <span class="badge count-badge">${dateScores.length} รายการ</span>
                  <div class="w-4 text-center">
                    ${(() => {
                      // ตรวจสอบว่าวันนี้มี error หรือไม่
                      const hasError = dateScores.some(score => 
                        score.isDuplicate || 
                        score.hasNoClinic || 
                        score.isSubmitBeforeWork || 
                        score.hasDuplicateWithOtherTeacher
                      );
                      return hasError 
                        ? '<i class="fas fa-exclamation text-red-500" title="พบรายการที่มีข้อผิดพลาด"></i>' 
                        : '';
                    })()}
                  </div>
                </div>
              </div>
              
              ${isExpanded ? renderScoreTable(date, dateScores) : ''}
            </div>
          `;
        });
        
        scoreGroupsElement.innerHTML = html;
        document.getElementById('entriesCount').textContent = 'แสดง ' + filteredScores.length + ' จาก ' + allScores.length + ' รายการ';
        
        // Add event listeners to checkboxes
        document.querySelectorAll('.score-checkbox').forEach(checkbox => {
          checkbox.addEventListener('change', function() {
            const scoreId = parseInt(this.getAttribute('data-id'));
            handleCardClick(scoreId);
          });
        });
        
        document.querySelectorAll('.date-checkbox').forEach(checkbox => {
          checkbox.addEventListener('change', function() {
            const date = this.getAttribute('data-date');
            handleDateSelection(date);
          });
        });
      }
      
      function renderScoreTable(date, scores) {
        const allSelected = scores.every(score => selectedScores.includes(score.id));
        
        // Sort scores by timestamp (latest first)
        scores.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        let html = `
          <div class="p-4">
            <div class="flex items-center mb-4">
              <input 
                type="checkbox" 
                class="date-checkbox checkbox mr-2" 
                data-date="${date}" 
                ${allSelected ? 'checked' : ''}
                aria-label="Select all for this date"
              >
              <span class="text-sm text-gray-600">เลือกทั้งหมด</span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        `;
        
        scores.forEach(score => {
          const isSelected = selectedScores.includes(score.id);
          const sessionIcon = sessionIcons[score.session] || 'fa-hand';
          const sessionClass = sessionClasses[score.session] || 'badge-work';
          const workColorClass = score.work.includes('Red or Yellow card only') ? 'badge-red' : getWorkColorClass(score.work);
          
          // Format work text using the new mapping function
          const workText = getShortWorkText(score.work);

          // เพิ่มคลาสสำหรับการ์ดที่ซ้ำกับอาจารย์ท่านอื่น
          const isDisabled = !score.isOurCard;
          const cardClasses = [
            'bg-white rounded-lg border shadow-sm hover:shadow-md transition-shadow',
            isSelected ? 'ring-2 ring-red-500 bg-red-50' : '',
            isDisabled ? 'opacity-60 cursor-not-allowed bg-gray-50' : 'cursor-pointer'
          ].filter(Boolean).join(' ');
          
          html += `
            <div class="${cardClasses}" onclick="${isDisabled ? '' : `handleCardClick(${score.id})`}">
              <div class="p-4">
                <div class="flex items-start">
                  <input 
                    type="checkbox" 
                    class="score-checkbox checkbox mt-1 mr-3" 
                    data-id="${score.id}" 
                    ${isSelected ? 'checked' : ''}
                    ${isDisabled ? 'disabled' : ''}
                    aria-label="Select score for ${score.studentName}"
                    onclick="event.stopPropagation()"
                  >
                  <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-2">
                      <div class="truncate">
                        <div class="font-medium text-gray-900">
                          ${(() => {
                            const prefix = score.studentName.match(/^(นาย|นางสาว)/)?.[0] || '';
                            const name = score.studentName.replace(/^(นาย|นางสาว)\s*/, '');
                            const parts = name.split(' ');
                            return `
                              <div class="space-y-0.5">
                                <div class="text-lg">
                                  <span class="text-xs text-gray-500 hidden md:inline">${prefix}</span>
                                  <span>${parts[0]}</span>
                                </div>
                                <div class="text-sm text-gray-600">${parts.slice(1).join(' ')}</div>
                                <div class="text-xs text-gray-500 font-normal">${score.studentId}</div>
                              </div>
                            `;
                          })()}
                        </div>
                      </div>
                      <div class="text-xs text-gray-500 whitespace-nowrap text-right space-y-0.5">
                        <div>Submitted</div>
                        <div>${formatDate(score.timestamp).date}</div>
                        <div>${formatDate(score.timestamp).time}</div>
                      </div>
                    </div>
                    <div class="flex flex-col gap-2">
                      <div class="flex items-center justify-between gap-2">
                        <div class="flex items-center flex-wrap gap-1">
                          <span class="badge ${score.session.includes('Red') || score.session.includes('Yellow') ? 'badge-red' : sessionClass}">
                            <i class="fas ${sessionIcon} mr-1"></i>
                            ${sessionDisplayText[score.session] || score.session}
                          </span>
                          <span class="badge ${workColorClass}" title="${score.work}">
                            ${workText}
                          </span>
                          ${(() => {
                            // แสดง scoreSum ถ้ามีข้อมูล
                            if (score.scoreSum) {
                              return `<span class="score-sum">${score.scoreSum}</span>`;
                            } else if (isLoadingAdditionalData) {
                              // แสดง placeholder ถ้ากำลังโหลดข้อมูลเพิ่มเติม
                              return `<span class="score-sum text-gray-300"><span class="dot-loading"></span></span>`;
                            }
                            return '';
                          })()}
                          ${(() => {
                            // แสดง redyellowSum ถ้ามีข้อมูล
                            let redYellowHtml = '';
                            if (score.redCards > 0 || score.yellowCards > 0) {
                              redYellowHtml = '<span class="red-yellow-sum">';
                              if (score.redCards > 0) {
                                redYellowHtml += `<span class="flex items-center"><span class="red-dot"></span>${score.redCards}</span>`;
                              }
                              if (score.yellowCards > 0) {
                                redYellowHtml += `<span class="flex items-center ml-1"><span class="yellow-dot"></span>${score.yellowCards}</span>`;
                              }
                              redYellowHtml += '</span>';
                            } else if (isLoadingAdditionalData && !score.scoreSum) {
                              // ไม่แสดง spinner ถ้ามี scoreSum แล้ว (ใช้ spinner ร่วมกัน)
                              redYellowHtml = '';
                            }
                            return redYellowHtml;
                          })()}
                        </div>
                        <div class="flex items-center gap-1">
                          ${score.hasDuplicateWithOtherTeacher ? `
                            <div class="flex flex-col items-end">
                              <i class="fa-solid fa-user-group text-red-500 text-xs"></i>
                              ${score.isOurCard ? `
                                <div class="text-[0.65rem] text-gray-500 text-right">
                                  ซ้ำกับ ${score.duplicateTeachers.map(t => {
                                    const workText = t.work.length > 22 
                                      ? t.work.substring(0, 22) + '...'
                                      : t.work;
                                    return `${t.email} (${getShortWorkText(t.work)})`;
                                  }).join(', ')}
                                </div>
                              ` : `
                                <div class="text-[0.65rem] text-gray-500 text-right">
                                  By ${score.email.split('@')[0]}
                                </div>
                              `}
                            </div>
                          ` : `
                            ${score.isDuplicate ? `
                              <i class="fa-regular fa-clone text-red-500 text-xs" title="พบ Daily ซ้ำ"></i>
                            ` : ''}
                          `}
                          ${score.hasNoClinic ? `
                            <i class="fa-solid fa-ban text-red-500 text-xs" title="คาบไม่มีคลินิก"></i>
                          ` : score.hasAdditionalClinicInfo ? `
                            <i class="fa-solid fa-calendar-plus text-green-500 text-xs" title="คาบคลินิกเพิ่มเติม: ${score.hasAdditionalClinicInfo}"></i>
                          ` : ''}
                          ${score.isSubmitBeforeWork ? `
                            <i class="fa-regular fa-calendar-plus text-red-500 text-xs" title="เดลี่ก่อนวันปฏิบัติงาน"></i>
                          ` : ''}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          `;
        });
        
        html += `
            </div>
          </div>
        `;
        
        return html;
      }
      
      function getWorkColorClass(workType) {
        const colorClasses = [
          'badge-green',
          'badge-pink',
          'badge-indigo',
          'badge-yellow',
          'badge-red',
          'badge-blue',
          'badge-purple',
          'badge-teal'
        ];
        
        const index = uniqueWorkTypes.indexOf(workType);
        
        if (index >= 0) {
          return colorClasses[index % colorClasses.length];
        }
        
        return 'badge-work';
      }

      // ฟังก์ชันสำหรับแปลงข้อความประเภทงานให้สั้นลง
      function getShortWorkText(workType) {
        const workMapping = {
          'Operator': 'Operator',
          'Assistant': 'Assist',
          'Assistant Pedo': 'Assist Pedo',
          'Advance surgical procedure': 'Adv Surg',
          'Treatment plan': 'Tx Plan',
          'Treatment plan (Prosth)': 'Tx Plan Prosth',
          'Phantom head/Block/Suture practice': 'Phantom/Block/Sut.',
          'Journal/Case study': 'Journal',
          'Research': 'Research',
          'Lab work': 'Lab',
          'Consult, Clear requirement, เป็นคนไข้ให้เพื่อน': 'Consult, Clear req',
          'Red or Yellow card only': 'Red/Yellow card'
        };

        return workMapping[workType] || workType;
      }
      
      function toggleDateExpansion(date) {
        // Check if this date has any selected scores
        const hasSelectedScores = filteredScores.some(score => {
          const scoreDate = new Date(score.workingDate).toISOString().split('T')[0];
          return scoreDate === date && selectedScores.includes(score.id);
        });
        
        // If date has selected scores, don't allow collapse
        if (hasSelectedScores) {
          if (!expandedDates.includes(date)) {
            expandedDates.push(date);
          }
          return;
        }
        
        const index = expandedDates.indexOf(date);
        if (index !== -1) {
          expandedDates.splice(index, 1);
        } else {
          expandedDates.push(date);
        }
        renderScores();
      }
      
      function handleCardClick(id) {
        if (selectedScores.includes(id)) {
          selectedScores = selectedScores.filter(scoreId => scoreId !== id);
        } else {
          selectedScores.push(id);
        }
        updateDeleteButton();
        renderScores();
      }
      
      function handleDateSelection(date) {
        const dateScores = filteredScores.filter(score => {
          const scoreDate = new Date(score.workingDate);
          const formattedScoreDate = scoreDate.toISOString().split('T')[0];
          return formattedScoreDate === date;
        });
        
        const dateScoreIds = dateScores.map(score => score.id);
        const allSelected = dateScoreIds.every(id => selectedScores.includes(id));
        
        if (allSelected) {
          selectedScores = selectedScores.filter(id => !dateScoreIds.includes(id));
        } else {
          dateScoreIds.forEach(id => {
            if (!selectedScores.includes(id)) {
              selectedScores.push(id);
            }
          });
        }
        
        updateDeleteButton();
        renderScores();
      }
      
      function updateDeleteButton() {
        const deleteButton = document.getElementById('deleteButton');
        const selectionCount = document.getElementById('selectionCount');
        
        deleteButton.disabled = selectedScores.length === 0;
        
        if (selectedScores.length > 0) {
          selectionCount.classList.remove('hidden');
          selectionCount.querySelector('span').textContent = ` ${selectedScores.length} รายการ`;
        } else {
          selectionCount.classList.add('hidden');
          selectionCount.querySelector('span').textContent = '';
        }
      }
      
      function showDeleteDialog() {
        const dialog = document.getElementById('deleteDialog');
        const message = document.getElementById('deleteConfirmMessage');
        const confirmButton = document.getElementById('confirmDelete');
        const selectedCount = document.getElementById('selectedCount');
        
        // Reset the confirm button state
        confirmButton.disabled = false;
        
        // Get details of selected scores
        const selectedDetails = filteredScores
          .filter(score => selectedScores.includes(score.id))
          .map(score => ({
            date: formatWorkingDate(score.workingDate),
            name: score.studentName,
            session: score.session,
            work: score.work
          }));
        
        // Group by date
        const groupedDetails = selectedDetails.reduce((acc, item) => {
          if (!acc[item.date]) {
            acc[item.date] = [];
          }
          acc[item.date].push(item);
          return acc;
        }, {});
        
        selectedCount.textContent = selectedScores.length;
        
        // Create summary message
        let summaryHtml = '';
        Object.entries(groupedDetails).forEach(([date, items]) => {
          summaryHtml += `
            <div class="text-left">
              <div class="font-medium mb-2">${date}</div>
              <ul class="ml-4 space-y-2">
                ${items.map(item => `
                  <li class="text-sm text-gray-600 flex items-start gap-2">
                    <span class="inline-block w-1.5 h-1.5 rounded-full bg-gray-400 mt-[0.45rem]"></span>
                    <span class="flex-1">${item.name} - ${item.session} - ${getShortWorkText(item.work)}</span>
                  </li>
                `).join('')}
              </ul>
            </div>
          `;
        });
        
        message.innerHTML = summaryHtml;
        dialog.classList.remove('hidden');
      }
      
      function hideDeleteDialog() {
        const dialog = document.getElementById('deleteDialog');
        const confirmButton = document.getElementById('confirmDelete');
        const deleteSuccess = document.getElementById('deleteSuccess');
        const deleteConfirmContent = document.getElementById('deleteConfirmContent');
        const cancelButton = document.getElementById('cancelDelete');
        const closeTimer = document.getElementById('closeTimer');
        
        // Reset dialog state
        confirmButton.disabled = false;
        confirmButton.classList.remove('hidden');
        deleteSuccess.classList.add('hidden');
        deleteConfirmContent.classList.remove('hidden');
        cancelButton.children[0].textContent = 'Cancel';
        closeTimer.classList.add('hidden');
        cancelButton.disabled = false;
        
        dialog.classList.add('hidden');
      }
      
      function deleteSelectedScores() {
        const confirmButton = document.getElementById('confirmDelete');
        const deleteSpinner = document.getElementById('deleteSpinner');
        const deleteSuccess = document.getElementById('deleteSuccess');
        const deleteConfirmContent = document.getElementById('deleteConfirmContent');
        const cancelButton = document.getElementById('cancelDelete');
        const closeTimer = document.getElementById('closeTimer');
        
        confirmButton.disabled = true;
        cancelButton.disabled = true;
        deleteSpinner.classList.remove('hidden');
        
        google.script.run
          .withSuccessHandler(function(result) {
            deleteSpinner.classList.add('hidden');
            deleteConfirmContent.classList.add('hidden');
            deleteSuccess.classList.remove('hidden');
            confirmButton.classList.add('hidden');
            cancelButton.children[0].textContent = 'Close';
            cancelButton.disabled = false;
            
            // Start countdown
            let seconds = 3;
            closeTimer.textContent = `(${seconds})`;
            closeTimer.classList.remove('hidden');
            
            const countdown = setInterval(() => {
              seconds--;
              closeTimer.textContent = `(${seconds})`;
              
              if (seconds === 0) {
                clearInterval(countdown);
                hideDeleteDialog();
              }
            }, 1000);
            
            selectedScores = [];
            loadScoreData(true);
          })
          .withFailureHandler(function(error) {
            alert('Error deleting scores: ' + error.message);
            confirmButton.disabled = false;
            cancelButton.disabled = false;
            deleteSpinner.classList.add('hidden');
          })
          .deleteScores(selectedScores);
      }
      
      function formatDate(dateString) {
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            console.error('Invalid date:', dateString);
            return 'Invalid date';
          }
          
          // สร้างฟังก์ชันสำหรับแปลงเดือนเป็นตัวย่อภาษาไทย
          const getThaiMonthAbbr = (month) => {
            const thaiMonths = [
              'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
              'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
            ];
            return thaiMonths[month];
          };
          
          const day = date.getDate();
          const month = getThaiMonthAbbr(date.getMonth());
          const year = (date.getFullYear() + 543).toString().slice(-2);
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          
          return {
            date: `${day} ${month} ${year}`,
            time: `${hours}:${minutes}`
          };
        } catch (error) {
          console.error('Error formatting date:', dateString, error);
          return { date: 'Error', time: 'Error' };
        }
      }
      
      function formatWorkingDate(dateString) {
        try {
          const date = new Date(dateString);
          if (isNaN(date.getTime())) {
            console.error('Invalid working date:', dateString);
            return 'Invalid date';
          }
          
          // สร้างฟังก์ชันสำหรับแปลงวันเป็นภาษาไทย
          const getThaiDayName = (day) => {
            const thaiDays = [
              'อาทิตย์', 'จันทร์', 'อังคาร', 'พุธ', 'พฤหัส', 'ศุกร์', 'เสาร์'
            ];
            return thaiDays[day];
          };
          
          // สร้างฟังก์ชันสำหรับแปลงเดือนเป็นตัวย่อภาษาไทย
          const getThaiMonthAbbr = (month) => {
            const thaiMonths = [
              'ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.',
              'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'
            ];
            return thaiMonths[month];
          };
          
          const dayName = getThaiDayName(date.getDay());
          const day = date.getDate();
          const month = getThaiMonthAbbr(date.getMonth());
          const year = (date.getFullYear() + 543).toString().slice(-2);
          
          return `${dayName} ${day} ${month} ${year}`;
        } catch (error) {
          console.error('Error formatting working date:', dateString, error);
          return 'Error formatting date';
        }
      }

      function toggleFilters() {
        const filtersDiv = document.getElementById('advancedFilters');
        const chevron = document.getElementById('filterChevron');
        
        if (filtersDiv.classList.contains('hidden')) {
          filtersDiv.classList.remove('hidden');
          chevron.style.transform = 'rotate(180deg)';
        } else {
          filtersDiv.classList.add('hidden');
          chevron.style.transform = 'rotate(0deg)';
        }
      }

      function clearDateFilter() {
        document.getElementById('workDateFilter').value = '';
        document.getElementById('clearDateButton').style.display = 'none';
        filterScores();
      }
      
      function clearAllFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('sessionFilter').value = 'all';
        document.getElementById('workFilter').value = 'all';
        document.getElementById('workDateFilter').value = '';
        document.getElementById('clearDateButton').style.display = 'none';
        
        // Clear error filters
        activeErrorFilters.clear();
        document.getElementById('duplicateFilterBadge').classList.remove('bg-gray-200', 'ring-gray-200');
        document.getElementById('duplicateFilterBadge').classList.add('bg-transparent');
        document.getElementById('duplicateFilterBadge').querySelector('i').classList.remove('text-gray-700');
        document.getElementById('duplicateFilterBadge').querySelector('i').classList.add('text-gray-400');
        document.getElementById('duplicateFilterBadge').querySelector('span').classList.remove('text-gray-700');
        document.getElementById('duplicateFilterBadge').querySelector('span').classList.add('text-gray-500');
        
        document.getElementById('sessionFilterBadge').classList.remove('bg-gray-200', 'ring-gray-200');
        document.getElementById('sessionFilterBadge').classList.add('bg-transparent');
        document.getElementById('sessionFilterBadge').querySelector('i').classList.remove('text-gray-700');
        document.getElementById('sessionFilterBadge').querySelector('i').classList.add('text-gray-400');
        document.getElementById('sessionFilterBadge').querySelector('span').classList.remove('text-gray-700');
        document.getElementById('sessionFilterBadge').querySelector('span').classList.add('text-gray-500');
        
        // Collapse advanced filters
        const filtersDiv = document.getElementById('advancedFilters');
        const chevron = document.getElementById('filterChevron');
        filtersDiv.classList.add('hidden');
        chevron.style.transform = 'rotate(0deg)';
        
        // Reset highlights
        updateFilterHighlights();
        
        // Expand only the latest date
        if (allScores.length > 0) {
          const newestDate = new Date(allScores[0].workingDate).toISOString().split('T')[0];
          expandedDates = [newestDate];
        } else {
          expandedDates = [];
        }
        
        filterScores();
      }

      function clearSelection() {
        selectedScores = [];
        updateDeleteButton();
        renderScores();
      }
    </script>
  </body>
</html>