<!DOCTYPE html>
<html lang="th">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ScaryScore - RSU Dent</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style type="text/tailwindcss">
    /* ลบ .card และ .container-main ออก เพราะใช้ container/mx-auto/w-full/lg:max-w-[75%] ตามตัวอย่าง */
  </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
  <header class="border-b bg-white">
    <div class="container mx-auto px-4 py-4">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
        <div class="flex justify-between items-center">
          <div class="flex flex-col">
            <h1 class="text-2xl font-bold text-gray-900">
              <i class="fa-solid fa-tooth mr-2"></i>Scary<span class="font-light">Score</span>
            </h1>
            <p class="text-sm text-gray-500 md:block hidden mt-1">ระบบประกาศคะแนนสอบ Dent RSU</p>
          </div>
        </div>
        <div id="user-info" class="text-sm text-gray-700 md:text-right"></div>
      </div>
    </div>
  </header>
  <main class="px-4 py-8">
    <div class="container mx-auto w-full lg:max-w-[75%] space-y-4">
      <div id="main-card" class="bg-white rounded-lg border shadow-sm p-4 mb-4">
        <div id="status" class="mb-4 text-center"></div>
        <div id="content"></div>
      </div>
    </div>
  </main>
  <footer class="text-center text-xs text-gray-400 py-2 border-t">&copy; 2024 College of Dental Medicine, RSU</footer>
  <script>
    // เรียกตรวจสอบ auth
    google.script.run.withSuccessHandler(function(email) {
      if (!email) {
        document.getElementById('status').innerHTML = '<span class="text-red-500">กรุณาเข้าสู่ระบบด้วยบัญชี @rsu.ac.th</span>';
        document.getElementById('content').innerHTML = '';
        document.getElementById('user-info').innerHTML = '';
      } else {
        document.getElementById('status').innerHTML = '<span class="text-green-600">เข้าสู่ระบบสำเร็จ</span>';
        document.getElementById('user-info').innerHTML = '<i class="fa-solid fa-user"></i> ' + email;
        document.getElementById('content').innerHTML = '<div class="text-center">ยินดีต้อนรับสู่ระบบประกาศคะแนน</div>';
      }
    }).checkRsuAuth();
  </script>
  <style>
  </style>
</body>
</html> 